# AI-OS Production Environment Configuration Template
# Copy this file to .env and fill in your production values

# ================================
# CORE SYSTEM CONFIGURATION
# ================================

# Environment
NODE_ENV=production
FLASK_ENV=production
PYTHONPATH=/app

# Domain and URLs
DOMAIN=your-domain.com
WEBHOOK_URL=https://your-domain.com
RETOOL_HOSTNAME=https://your-domain.com

# ================================
# DATABASE CONFIGURATION
# ================================

# Supabase (Primary Database)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_role_key
PG_CONN_STRING_READONLY=postgres://user:pass@host:port/database

# Local PostgreSQL (for Retool)
DATABASE_URL=************************************************/retool

# ================================
# API KEYS AND INTEGRATIONS
# ================================

# OpenAI
OPENAI_API_KEY=sk-your_openai_api_key

# Anthropic (Claude)
ANTHROPIC_API_KEY=sk-ant-your_anthropic_api_key

# GoHighLevel
GHL_API_KEY=your_ghl_private_integration_api_key
GHL_LOCATION_ID=your_ghl_location_id
GHL_LOCATION_KEY=your_ghl_location_api_key
GHL_BASE_URL=https://services.leadconnectorhq.com

# Real Estate Data APIs
RENTCAST_API_KEY=your_rentcast_api_key
MELISSA_API_KEY=your_melissa_api_key

# ================================
# RETOOL CONFIGURATION
# ================================

# Retool API and Security
RETOOL_API_KEY=retool_your_generated_api_key
RETOOL_ORG_ID=your_retool_org_id
RETOOL_JWT_SECRET=your_secure_jwt_secret_min_32_chars
RETOOL_ENCRYPTION_KEY=your_secure_encryption_key_min_32_chars
RETOOL_LICENSE_KEY=your_retool_license_key_if_applicable
RETOOL_RESTRICTED_DOMAIN=your-domain.com

# ================================
# SECURITY CONFIGURATION
# ================================

# API Security
API_KEY=your_secure_api_key_for_main_access
READONLY_API_KEY=your_readonly_api_key
WEBHOOK_SECRET=your_webhook_secret_for_signature_verification
GHL_WEBHOOK_SECRET=your_ghl_webhook_secret

# JWT Configuration
JWT_SECRET=your_jwt_secret_min_32_characters_long
JWT_EXPIRY_HOURS=24

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# IP Whitelist (comma-separated, leave empty to disable)
ALLOWED_IPS=

# ================================
# MONITORING AND ALERTING
# ================================

# Email Notifications
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
ALERT_EMAIL=<EMAIL>
NOTIFICATION_EMAIL=<EMAIL>

# Slack Notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook

# ================================
# BACKUP CONFIGURATION
# ================================

# Backup Settings
BACKUP_DIR=/opt/ai-os-backups
RETENTION_DAYS=30
BACKUP_ENCRYPTION_KEY=your_backup_encryption_key_min_32_chars

# AWS S3 for Cloud Backups
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_DEFAULT_REGION=us-east-1
S3_BUCKET=your-ai-os-backups-bucket

# ================================
# KNOWLEDGE PIPELINE
# ================================

# Knowledge Base Configuration
SUPABASE_KB_TABLE_NAME=knowledge_base
DEFAULT_CHUNK_SIZE=500
DEFAULT_OVERLAP=50
KNOWLEDGE_PIPELINE_DEBUG=false

# ================================
# LOGGING CONFIGURATION
# ================================

# Log Levels
LOG_LEVEL=INFO
API_LOG_LEVEL=INFO
KNOWLEDGE_LOG_LEVEL=INFO

# Log Retention
LOG_RETENTION_DAYS=30
LOG_MAX_SIZE=100MB

# ================================
# PERFORMANCE TUNING
# ================================

# API Configuration
API_HOST=0.0.0.0
API_PORT=5002
API_WORKERS=4
API_TIMEOUT=300

# Database Connection Pooling
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30

# Cache Configuration
REDIS_URL=redis://localhost:6379/0
CACHE_TTL=3600

# ================================
# FEATURE FLAGS
# ================================

# Enable/Disable Features
ENABLE_KNOWLEDGE_PIPELINE=true
ENABLE_AGENT_LEARNING=true
ENABLE_ERROR_RECOVERY=true
ENABLE_MONITORING=true
ENABLE_RATE_LIMITING=true
ENABLE_IP_WHITELIST=false
ENABLE_WEBHOOK_SIGNATURE_VERIFICATION=true

# ================================
# DEVELOPMENT/DEBUG (Production: false)
# ================================

# Debug Settings (SET TO FALSE IN PRODUCTION)
DEBUG=false
FLASK_DEBUG=false
ENABLE_CORS=false
SKIP_AUTH=false

# ================================
# DOCKER CONFIGURATION
# ================================

# Docker Compose Settings
COMPOSE_PROJECT_NAME=ai-os
COMPOSE_FILE=docker-compose.yml

# Container Resource Limits
API_MEMORY_LIMIT=2g
RETOOL_MEMORY_LIMIT=4g
POSTGRES_MEMORY_LIMIT=2g

# ================================
# SSL/TLS CONFIGURATION
# ================================

# SSL Settings (Managed by Caddy)
SSL_EMAIL=<EMAIL>
CADDY_ADMIN_LISTEN=localhost:2019

# ================================
# EXTERNAL SERVICES
# ================================

# Third-party Service URLs
PRIVY_ORO_URL=https://privy.oro
BATCHLEADS_URL=https://batchleads.io
LOTSIDE_URL=https://lotside.com

# ================================
# SYSTEM LIMITS
# ================================

# File Upload Limits
MAX_UPLOAD_SIZE=10MB
MAX_KNOWLEDGE_DOCS=1000

# Processing Limits
MAX_CONCURRENT_LEADS=10
MAX_RETRY_ATTEMPTS=3
CIRCUIT_BREAKER_THRESHOLD=5

# ================================
# COMPLIANCE AND PRIVACY
# ================================

# Data Retention
LEAD_DATA_RETENTION_DAYS=2555  # 7 years
LOG_DATA_RETENTION_DAYS=90
ANALYTICS_DATA_RETENTION_DAYS=365

# Privacy Settings
ANONYMIZE_LOGS=true
ENCRYPT_SENSITIVE_DATA=true
GDPR_COMPLIANCE=true

# ================================
# HEALTH CHECK CONFIGURATION
# ================================

# Health Check Intervals (seconds)
HEALTH_CHECK_INTERVAL=30
DATABASE_HEALTH_CHECK_TIMEOUT=10
API_HEALTH_CHECK_TIMEOUT=5
EXTERNAL_SERVICE_TIMEOUT=15

# ================================
# NOTES FOR PRODUCTION SETUP
# ================================

# 1. Generate secure random values for all secrets:
#    - Use: openssl rand -base64 32
#    - Minimum 32 characters for encryption keys
#    - Use different values for each secret

# 2. Database Security:
#    - Use read-only user for Retool connections
#    - Enable SSL for all database connections
#    - Regular backup verification

# 3. API Security:
#    - Rotate API keys monthly
#    - Use different keys for different access levels
#    - Monitor API usage and rate limits

# 4. Monitoring:
#    - Set up email/Slack alerts
#    - Monitor disk space, memory, CPU
#    - Set up log aggregation

# 5. Backup Strategy:
#    - Daily automated backups
#    - Test restore procedures monthly
#    - Store backups in multiple locations

# 6. SSL/TLS:
#    - Caddy handles automatic SSL certificates
#    - Ensure domain DNS points to your server
#    - Test SSL configuration regularly

# 7. Performance:
#    - Monitor response times
#    - Adjust worker counts based on load
#    - Implement caching where appropriate

# 8. Compliance:
#    - Review data retention policies
#    - Implement proper logging
#    - Regular security audits
