{"appInfo": {"appTemplate": {"version": "3.41.0", "type": "app", "slug": "knowledge-management", "name": "AI-OS Knowledge Management", "description": "Upload and manage documents for AI agent learning and deal analysis", "folders": [], "globalShortcuts": [], "globalInfo": {"urlParameters": [], "setupSql": [], "setupJs": []}}}, "page": {"id": "page1", "layout": [{"id": "headerContainer", "type": "containerWidget", "position": {"x": 0, "y": 0, "width": 12, "height": 1}, "children": [{"id": "titleText", "type": "textWidget", "position": {"x": 0, "y": 0, "width": 8, "height": 1}, "properties": {"value": "## 🧠 AI-OS Knowledge Management", "textAlign": "left"}}, {"id": "refreshButton", "type": "buttonWidget", "position": {"x": 10, "y": 0, "width": 2, "height": 1}, "properties": {"text": "Refresh", "buttonType": "secondary", "action": "{{getDocumentsQuery.trigger()}}"}}]}, {"id": "uploadSection", "type": "containerWidget", "position": {"x": 0, "y": 1, "width": 12, "height": 4}, "properties": {"title": "📄 Document Upload", "showBorder": true}, "children": [{"id": "fileUploader", "type": "fileInputWidget", "position": {"x": 0, "y": 0, "width": 6, "height": 2}, "properties": {"label": "Select Document", "acceptedFileTypes": [".pdf", ".txt", ".md", ".csv", ".docx"], "maxFileSize": "10MB", "multiple": false}}, {"id": "documentTypeSelect", "type": "selectWidget", "position": {"x": 6, "y": 0, "width": 3, "height": 1}, "properties": {"label": "Document Type", "options": [{"value": "deal_analysis", "label": "Deal Analysis Template"}, {"value": "mao_calculation", "label": "MAO Calculation Method"}, {"value": "market_analysis", "label": "Market Analysis Guide"}, {"value": "negotiation_strategy", "label": "Negotiation Strategy"}, {"value": "contract_template", "label": "Contract Template"}, {"value": "follow_up_sequence", "label": "Follow-up Sequence"}, {"value": "general_knowledge", "label": "General Knowledge"}], "defaultValue": "general_knowledge"}}, {"id": "prioritySelect", "type": "selectWidget", "position": {"x": 9, "y": 0, "width": 3, "height": 1}, "properties": {"label": "Priority Level", "options": [{"value": "critical", "label": "🔴 Critical"}, {"value": "high", "label": "🟡 High"}, {"value": "medium", "label": "🟢 Medium"}, {"value": "low", "label": "⚪ Low"}], "defaultValue": "medium"}}, {"id": "tagsInput", "type": "textInputWidget", "position": {"x": 0, "y": 1, "width": 6, "height": 1}, "properties": {"label": "Tags (comma-separated)", "placeholder": "e.g., wholesaling, mao, calculations, strategy"}}, {"id": "descriptionInput", "type": "textAreaWidget", "position": {"x": 6, "y": 1, "width": 6, "height": 1}, "properties": {"label": "Description", "placeholder": "Describe what this document teaches the AI..."}}, {"id": "uploadButton", "type": "buttonWidget", "position": {"x": 0, "y": 2, "width": 3, "height": 1}, "properties": {"text": "📤 Upload Document", "buttonType": "primary", "disabled": "{{!fileUploader.files || fileUploader.files.length === 0}}", "action": "{{uploadDocumentQuery.trigger()}}"}}, {"id": "uploadStatus", "type": "textWidget", "position": {"x": 3, "y": 2, "width": 9, "height": 1}, "properties": {"value": "{{uploadDocumentQuery.isFetching ? '⏳ Uploading and processing...' : (uploadDocumentQuery.data ? '✅ ' + uploadDocumentQuery.data.message : '')}}"}}]}, {"id": "documentsTable", "type": "tableWidget", "position": {"x": 0, "y": 5, "width": 12, "height": 6}, "properties": {"title": "📚 Knowledge Base Documents", "data": "{{getDocumentsQuery.data || []}}", "columns": [{"id": "filename", "label": "Document", "type": "text", "width": "200px"}, {"id": "document_type", "label": "Type", "type": "text", "width": "150px"}, {"id": "tags", "label": "Tags", "type": "text", "width": "200px"}, {"id": "chunks_count", "label": "Chunks", "type": "number", "width": "80px"}, {"id": "created_at", "label": "Uploaded", "type": "datetime", "width": "150px"}, {"id": "priority", "label": "Priority", "type": "text", "width": "100px"}], "searchable": true, "sortable": true, "pagination": true, "pageSize": 20, "actionButton": {"text": "View Details", "action": "{{drawerDocumentDetails.open()}}"}}}, {"id": "drawerDocumentDetails", "type": "drawerWidget", "properties": {"title": "Document Details: {{documentsTable.selectedRow.data.filename}}", "width": "60%"}, "children": [{"id": "documentDetailsContainer", "type": "containerWidget", "position": {"x": 0, "y": 0, "width": 12, "height": 8}, "children": [{"id": "documentMetadata", "type": "textWidget", "position": {"x": 0, "y": 0, "width": 12, "height": 3}, "properties": {"value": "**Filename:** {{documentsTable.selectedRow.data.filename}}\n**Type:** {{documentsTable.selectedRow.data.document_type}}\n**Tags:** {{documentsTable.selectedRow.data.tags}}\n**Chunks:** {{documentsTable.selectedRow.data.chunks_count}}\n**Priority:** {{documentsTable.selectedRow.data.priority}}\n**Uploaded:** {{documentsTable.selectedRow.data.created_at}}\n**Description:** {{documentsTable.selectedRow.data.description}}"}}, {"id": "documentChunksTable", "type": "tableWidget", "position": {"x": 0, "y": 3, "width": 12, "height": 4}, "properties": {"title": "Document Chunks", "data": "{{getDocumentChunksQuery.data || []}}", "columns": [{"id": "chunk_index", "label": "Index", "type": "number", "width": "60px"}, {"id": "content_preview", "label": "Content Preview", "type": "text", "width": "400px"}, {"id": "chunk_size", "label": "Size", "type": "number", "width": "80px"}], "searchable": true, "pageSize": 10}}, {"id": "deleteDocumentButton", "type": "buttonWidget", "position": {"x": 0, "y": 7, "width": 3, "height": 1}, "properties": {"text": "🗑️ Delete Document", "buttonType": "danger", "action": "{{deleteDocumentQuery.trigger()}}"}}]}]}]}, "queries": [{"id": "uploadDocumentQuery", "type": "restApi<PERSON><PERSON>y", "resourceName": "AI_OS_API", "query": {"method": "POST", "url": "/api/v1/documents/ingest-file", "headers": {"Authorization": "Bearer {{ RETOOL_API_KEY }}"}, "body": {"type": "form-data", "formData": [{"key": "file", "value": "{{fileUploader.files[0]}}", "type": "file"}, {"key": "tags", "value": "{{tagsInput.value + ',' + documentTypeSelect.value + ',' + prioritySelect.value}}", "type": "text"}, {"key": "user_metadata", "value": "{{JSON.stringify({document_type: documentTypeSelect.value, priority: prioritySelect.value, description: descriptionInput.value, uploaded_via: 'retool'})}}", "type": "text"}]}}, "runOnPageLoad": false, "showErpNotification": true, "onSuccess": ["{{getDocumentsQuery.trigger()}}", "{{fileUploader.clearValue()}}", "{{tagsInput.clearValue()}}", "{{descriptionInput.clearValue()}}"]}, {"id": "getDocumentsQuery", "type": "restApi<PERSON><PERSON>y", "resourceName": "AI_OS_API", "query": {"method": "GET", "url": "/api/v1/documents/list", "headers": {"Authorization": "Bearer {{ RETOOL_API_KEY }}"}}, "runOnPageLoad": true, "showErpNotification": false}, {"id": "getDocumentChunksQuery", "type": "restApi<PERSON><PERSON>y", "resourceName": "AI_OS_API", "query": {"method": "GET", "url": "/api/v1/documents/{{documentsTable.selectedRow.data.document_id}}/chunks", "headers": {"Authorization": "Bearer {{ RETOOL_API_KEY }}"}}, "runOnPageLoad": false, "showErpNotification": false, "trigger": "{{documentsTable.selectedRow}}"}, {"id": "deleteDocumentQuery", "type": "restApi<PERSON><PERSON>y", "resourceName": "AI_OS_API", "query": {"method": "DELETE", "url": "/api/v1/documents/{{documentsTable.selectedRow.data.document_id}}", "headers": {"Authorization": "Bearer {{ RETOOL_API_KEY }}"}}, "runOnPageLoad": false, "showErpNotification": true, "onSuccess": ["{{getDocumentsQuery.trigger()}}", "{{drawerDocumentDetails.close()}}"]}], "resources": [{"name": "AI_OS_API", "type": "restapi", "baseUrl": "{{ WEBHOOK_URL || 'http://localhost:5002' }}", "headers": {"Content-Type": "application/json"}}]}