-- Migration: Deal Tracking Schema
-- Description: Tables for comprehensive deal tracking, analysis, and automation metrics
-- Created: 2025-01-XX

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Table: deal_analysis
-- Stores comprehensive deal analysis results
CREATE TABLE IF NOT EXISTS deal_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lead_id UUID REFERENCES leads(id) ON DELETE CASCADE,
    property_id UUID REFERENCES properties(id) ON DELETE SET NULL,
    analysis_timestamp TIMESTAMPTZ DEFAULT NOW(),
    
    -- Property valuation data
    arv NUMERIC,
    confidence_score NUMERIC,
    comp_count INTEGER,
    valuation_range JSONB, -- {low, high, conservative}
    
    -- Market analysis
    market_trend TEXT, -- hot, stable, slow
    avg_days_on_market NUMERIC,
    avg_price_per_sqft NUMERIC,
    market_activity TEXT, -- high, moderate, low
    
    -- Strategy analysis results
    wholesale_analysis JSONB,
    flip_analysis JSONB,
    brrrr_analysis JSONB,
    buy_hold_analysis JSONB,
    
    -- Deal scores (0-100)
    wholesale_score NUMERIC DEFAULT 0,
    flip_score NUMERIC DEFAULT 0,
    brrrr_score NUMERIC DEFAULT 0,
    buy_hold_score NUMERIC DEFAULT 0,
    overall_score NUMERIC DEFAULT 0,
    
    -- Risk assessment
    risk_level TEXT, -- low, medium, high
    risk_factors TEXT[],
    risk_score NUMERIC DEFAULT 0,
    
    -- Recommendations
    best_strategy TEXT,
    recommendations TEXT[],
    
    -- Raw analysis data
    raw_analysis_data JSONB,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table: offers
-- Tracks generated offers and their status
CREATE TABLE IF NOT EXISTS offers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lead_id UUID REFERENCES leads(id) ON DELETE CASCADE,
    property_id UUID REFERENCES properties(id) ON DELETE SET NULL,
    deal_analysis_id UUID REFERENCES deal_analysis(id) ON DELETE SET NULL,
    
    -- Offer details
    offer_amount NUMERIC NOT NULL,
    strategy TEXT NOT NULL, -- wholesale, flip, brrrr, buy_hold
    mao NUMERIC, -- Maximum Allowable Offer
    
    -- Offer terms
    closing_timeline TEXT, -- e.g., "30 days", "cash", "subject to inspection"
    contingencies TEXT[],
    earnest_money NUMERIC,
    
    -- Delivery information
    delivery_method TEXT[], -- email, sms, phone, mail
    delivered_at TIMESTAMPTZ,
    template_used TEXT,
    
    -- Response tracking
    status TEXT DEFAULT 'sent', -- sent, viewed, responded, accepted, rejected, counter_offer, expired
    response_date TIMESTAMPTZ,
    response_type TEXT, -- acceptance, rejection, counter_offer, request_info
    response_details JSONB,
    
    -- Counter offer tracking
    counter_offer_amount NUMERIC,
    counter_offer_terms JSONB,
    
    -- Follow-up tracking
    follow_up_count INTEGER DEFAULT 0,
    last_follow_up TIMESTAMPTZ,
    next_follow_up TIMESTAMPTZ,
    
    -- Automation tracking
    auto_generated BOOLEAN DEFAULT true,
    generated_by TEXT, -- agent name or user
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table: deal_pipeline
-- Tracks deals through the pipeline stages
CREATE TABLE IF NOT EXISTS deal_pipeline (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lead_id UUID REFERENCES leads(id) ON DELETE CASCADE,
    offer_id UUID REFERENCES offers(id) ON DELETE SET NULL,
    
    -- Pipeline stage
    stage TEXT NOT NULL, -- lead_received, lead_qualified, property_analyzed, offer_generated, offer_sent, under_review, accepted, rejected, closed, lost
    previous_stage TEXT,
    stage_entered_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Stage-specific data
    stage_data JSONB,
    
    -- Automation tracking
    automated_transition BOOLEAN DEFAULT false,
    transition_trigger TEXT, -- webhook, scheduled, manual, agent_action
    
    -- Performance metrics
    time_in_stage INTERVAL,
    total_pipeline_time INTERVAL,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table: automation_metrics
-- Tracks automation system performance
CREATE TABLE IF NOT EXISTS automation_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Timestamp and period
    metric_date DATE DEFAULT CURRENT_DATE,
    metric_hour INTEGER DEFAULT EXTRACT(HOUR FROM NOW()),
    
    -- Lead processing metrics
    leads_processed INTEGER DEFAULT 0,
    hot_leads_detected INTEGER DEFAULT 0,
    tier_1_leads INTEGER DEFAULT 0,
    tier_2_leads INTEGER DEFAULT 0,
    tier_3_leads INTEGER DEFAULT 0,
    
    -- Analysis metrics
    properties_analyzed INTEGER DEFAULT 0,
    comping_requests INTEGER DEFAULT 0,
    mao_calculations INTEGER DEFAULT 0,
    
    -- Offer metrics
    offers_generated INTEGER DEFAULT 0,
    offers_sent INTEGER DEFAULT 0,
    offer_responses INTEGER DEFAULT 0,
    offer_acceptances INTEGER DEFAULT 0,
    
    -- Communication metrics
    emails_sent INTEGER DEFAULT 0,
    sms_sent INTEGER DEFAULT 0,
    eric_notifications INTEGER DEFAULT 0,
    follow_ups_sent INTEGER DEFAULT 0,
    
    -- System metrics
    api_requests INTEGER DEFAULT 0,
    errors_encountered INTEGER DEFAULT 0,
    system_uptime_minutes INTEGER DEFAULT 0,
    
    -- Performance metrics
    avg_processing_time_seconds NUMERIC DEFAULT 0,
    avg_response_time_ms NUMERIC DEFAULT 0,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table: workflow_executions
-- Tracks individual workflow executions for debugging and optimization
CREATE TABLE IF NOT EXISTS workflow_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Execution details
    workflow_type TEXT NOT NULL, -- master_pipeline, deal_analysis, comping, offer_generation
    lead_id UUID REFERENCES leads(id) ON DELETE SET NULL,
    execution_id TEXT, -- unique execution identifier
    
    -- Timing
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    duration_seconds NUMERIC,
    
    -- Status
    status TEXT DEFAULT 'running', -- running, completed, failed, timeout
    success BOOLEAN,
    error_message TEXT,
    
    -- Execution data
    input_data JSONB,
    output_data JSONB,
    steps_completed TEXT[],
    current_step TEXT,
    
    -- Performance
    steps_total INTEGER,
    steps_successful INTEGER,
    steps_failed INTEGER,
    
    -- Automation context
    triggered_by TEXT, -- webhook, schedule, manual, agent
    trigger_data JSONB,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table: system_alerts
-- Stores system alerts and notifications
CREATE TABLE IF NOT EXISTS system_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Alert details
    alert_type TEXT NOT NULL, -- error, warning, info, success
    severity TEXT DEFAULT 'medium', -- low, medium, high, critical
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    
    -- Context
    component TEXT, -- ghl_mcp, master_orchestrator, deal_analysis, etc.
    lead_id UUID REFERENCES leads(id) ON DELETE SET NULL,
    workflow_execution_id UUID REFERENCES workflow_executions(id) ON DELETE SET NULL,
    
    -- Status
    status TEXT DEFAULT 'active', -- active, acknowledged, resolved, dismissed
    acknowledged_at TIMESTAMPTZ,
    acknowledged_by TEXT,
    resolved_at TIMESTAMPTZ,
    
    -- Notification tracking
    notifications_sent INTEGER DEFAULT 0,
    last_notification_sent TIMESTAMPTZ,
    
    -- Alert data
    alert_data JSONB,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_deal_analysis_lead_id ON deal_analysis(lead_id);
CREATE INDEX IF NOT EXISTS idx_deal_analysis_timestamp ON deal_analysis(analysis_timestamp);
CREATE INDEX IF NOT EXISTS idx_deal_analysis_best_strategy ON deal_analysis(best_strategy);
CREATE INDEX IF NOT EXISTS idx_deal_analysis_overall_score ON deal_analysis(overall_score);

CREATE INDEX IF NOT EXISTS idx_offers_lead_id ON offers(lead_id);
CREATE INDEX IF NOT EXISTS idx_offers_status ON offers(status);
CREATE INDEX IF NOT EXISTS idx_offers_strategy ON offers(strategy);
CREATE INDEX IF NOT EXISTS idx_offers_delivered_at ON offers(delivered_at);
CREATE INDEX IF NOT EXISTS idx_offers_response_date ON offers(response_date);

CREATE INDEX IF NOT EXISTS idx_deal_pipeline_lead_id ON deal_pipeline(lead_id);
CREATE INDEX IF NOT EXISTS idx_deal_pipeline_stage ON deal_pipeline(stage);
CREATE INDEX IF NOT EXISTS idx_deal_pipeline_stage_entered ON deal_pipeline(stage_entered_at);

CREATE INDEX IF NOT EXISTS idx_automation_metrics_date ON automation_metrics(metric_date);
CREATE INDEX IF NOT EXISTS idx_automation_metrics_hour ON automation_metrics(metric_date, metric_hour);

CREATE INDEX IF NOT EXISTS idx_workflow_executions_type ON workflow_executions(workflow_type);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_lead_id ON workflow_executions(lead_id);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_status ON workflow_executions(status);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_started ON workflow_executions(started_at);

CREATE INDEX IF NOT EXISTS idx_system_alerts_type ON system_alerts(alert_type);
CREATE INDEX IF NOT EXISTS idx_system_alerts_severity ON system_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_system_alerts_status ON system_alerts(status);
CREATE INDEX IF NOT EXISTS idx_system_alerts_component ON system_alerts(component);

-- Create views for common queries

-- View: active_deals
-- Shows deals currently in the pipeline
CREATE OR REPLACE VIEW active_deals AS
SELECT 
    dp.id as pipeline_id,
    l.id as lead_id,
    l.first_name,
    l.last_name,
    l.email,
    l.phone,
    p.address,
    dp.stage,
    dp.stage_entered_at,
    dp.time_in_stage,
    o.offer_amount,
    o.status as offer_status,
    da.best_strategy,
    da.overall_score
FROM deal_pipeline dp
JOIN leads l ON dp.lead_id = l.id
LEFT JOIN properties p ON l.property_id = p.id
LEFT JOIN offers o ON dp.offer_id = o.id
LEFT JOIN deal_analysis da ON da.lead_id = l.id
WHERE dp.stage NOT IN ('closed', 'lost', 'rejected')
ORDER BY dp.stage_entered_at DESC;

-- View: daily_metrics
-- Aggregated daily automation metrics
CREATE OR REPLACE VIEW daily_metrics AS
SELECT 
    metric_date,
    SUM(leads_processed) as total_leads_processed,
    SUM(hot_leads_detected) as total_hot_leads,
    SUM(offers_generated) as total_offers_generated,
    SUM(offers_sent) as total_offers_sent,
    SUM(offer_acceptances) as total_acceptances,
    CASE 
        WHEN SUM(offers_sent) > 0 
        THEN ROUND((SUM(offer_acceptances)::NUMERIC / SUM(offers_sent) * 100), 2)
        ELSE 0 
    END as acceptance_rate_percent,
    SUM(emails_sent) as total_emails,
    SUM(sms_sent) as total_sms,
    SUM(errors_encountered) as total_errors,
    AVG(avg_processing_time_seconds) as avg_processing_time
FROM automation_metrics
GROUP BY metric_date
ORDER BY metric_date DESC;

-- View: pipeline_summary
-- Summary of deals by pipeline stage
CREATE OR REPLACE VIEW pipeline_summary AS
SELECT 
    stage,
    COUNT(*) as deal_count,
    AVG(EXTRACT(EPOCH FROM time_in_stage)/3600) as avg_hours_in_stage,
    MIN(stage_entered_at) as oldest_deal,
    MAX(stage_entered_at) as newest_deal
FROM deal_pipeline
WHERE stage NOT IN ('closed', 'lost')
GROUP BY stage
ORDER BY 
    CASE stage
        WHEN 'lead_received' THEN 1
        WHEN 'lead_qualified' THEN 2
        WHEN 'property_analyzed' THEN 3
        WHEN 'offer_generated' THEN 4
        WHEN 'offer_sent' THEN 5
        WHEN 'under_review' THEN 6
        WHEN 'accepted' THEN 7
        ELSE 8
    END;

-- Add triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_deal_analysis_updated_at BEFORE UPDATE ON deal_analysis FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_offers_updated_at BEFORE UPDATE ON offers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_system_alerts_updated_at BEFORE UPDATE ON system_alerts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
