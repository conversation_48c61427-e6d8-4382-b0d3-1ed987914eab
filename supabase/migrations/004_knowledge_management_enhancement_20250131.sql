-- Knowledge Management Enhancement Migration
-- Adds support for structured knowledge extraction and document management

-- Update knowledge_base table to support enhanced document management
ALTER TABLE knowledge_base 
ADD COLUMN IF NOT EXISTS document_id TEXT,
ADD COLUMN IF NOT EXISTS source_type TEXT DEFAULT 'file_upload',
ADD COLUMN IF NOT EXISTS chunk_index INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS priority TEXT DEFAULT 'medium',
ADD COLUMN IF NOT EXISTS structured_knowledge JSONB;

-- Create index on document_id for faster queries
CREATE INDEX IF NOT EXISTS idx_knowledge_base_document_id ON knowledge_base(document_id);
CREATE INDEX IF NOT EXISTS idx_knowledge_base_source_type ON knowledge_base(source_type);
CREATE INDEX IF NOT EXISTS idx_knowledge_base_priority ON knowledge_base(priority);

-- Create index on structured knowledge for faster searches
CREATE INDEX IF NOT EXISTS idx_knowledge_base_structured_knowledge ON knowledge_base USING GIN(structured_knowledge);

-- Create document_templates table for storing reusable templates
CREATE TABLE IF NOT EXISTS document_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_name TEXT NOT NULL UNIQUE,
    template_type TEXT NOT NULL,
    description TEXT,
    template_content JSONB NOT NULL,
    variables JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- Create agent_learning_feedback table for continuous improvement
CREATE TABLE IF NOT EXISTS agent_learning_feedback (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    lead_id TEXT,
    agent_name TEXT NOT NULL,
    action_taken TEXT NOT NULL,
    outcome TEXT NOT NULL,
    feedback_type TEXT NOT NULL, -- 'success', 'failure', 'improvement'
    feedback_data JSONB NOT NULL,
    knowledge_applied JSONB, -- What knowledge was used
    lessons_learned JSONB, -- What was learned from this interaction
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for agent learning feedback
CREATE INDEX IF NOT EXISTS idx_agent_learning_feedback_lead_id ON agent_learning_feedback(lead_id);
CREATE INDEX IF NOT EXISTS idx_agent_learning_feedback_agent_name ON agent_learning_feedback(agent_name);
CREATE INDEX IF NOT EXISTS idx_agent_learning_feedback_type ON agent_learning_feedback(feedback_type);
CREATE INDEX IF NOT EXISTS idx_agent_learning_feedback_created_at ON agent_learning_feedback(created_at);

-- Create knowledge_usage_analytics table for tracking knowledge utilization
CREATE TABLE IF NOT EXISTS knowledge_usage_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id TEXT NOT NULL,
    knowledge_chunk_id UUID REFERENCES knowledge_base(id),
    agent_name TEXT NOT NULL,
    usage_context TEXT NOT NULL, -- 'mao_calculation', 'deal_analysis', 'negotiation', etc.
    lead_id TEXT,
    usage_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    effectiveness_score DECIMAL(3,2), -- 0.00 to 1.00
    usage_metadata JSONB DEFAULT '{}'
);

-- Create indexes for knowledge usage analytics
CREATE INDEX IF NOT EXISTS idx_knowledge_usage_document_id ON knowledge_usage_analytics(document_id);
CREATE INDEX IF NOT EXISTS idx_knowledge_usage_agent_name ON knowledge_usage_analytics(agent_name);
CREATE INDEX IF NOT EXISTS idx_knowledge_usage_context ON knowledge_usage_analytics(usage_context);
CREATE INDEX IF NOT EXISTS idx_knowledge_usage_timestamp ON knowledge_usage_analytics(usage_timestamp);

-- Create deal_calculation_templates table for MAO and analysis templates
CREATE TABLE IF NOT EXISTS deal_calculation_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_name TEXT NOT NULL UNIQUE,
    template_type TEXT NOT NULL, -- 'mao', 'deal_analysis', 'market_analysis'
    description TEXT,
    formulas JSONB NOT NULL,
    variables JSONB NOT NULL,
    decision_criteria JSONB DEFAULT '{}',
    examples JSONB DEFAULT '[]',
    priority TEXT DEFAULT 'medium',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for deal calculation templates
CREATE INDEX IF NOT EXISTS idx_deal_calc_templates_type ON deal_calculation_templates(template_type);
CREATE INDEX IF NOT EXISTS idx_deal_calc_templates_priority ON deal_calculation_templates(priority);
CREATE INDEX IF NOT EXISTS idx_deal_calc_templates_active ON deal_calculation_templates(is_active);

-- Insert default MAO calculation template
INSERT INTO deal_calculation_templates (
    template_name,
    template_type,
    description,
    formulas,
    variables,
    decision_criteria,
    examples,
    priority
) VALUES (
    'Standard Wholesaling MAO',
    'mao',
    'Standard Maximum Allowable Offer calculation for wholesaling',
    '{
        "mao": "ARV * 0.70 - Repair_Costs - Holding_Costs - Assignment_Fee",
        "arv": "Comparable_Sales_Average",
        "repair_costs": "Estimated_Repairs + 10% Contingency",
        "holding_costs": "Monthly_Costs * Estimated_Months",
        "assignment_fee": "Minimum_Profit_Target"
    }',
    '{
        "arv_multiplier": 0.70,
        "contingency_percentage": 0.10,
        "minimum_profit": 5000,
        "max_repair_percentage": 0.30
    }',
    '{
        "min_profit_margin": 5000,
        "max_repair_costs": "ARV * 0.30",
        "target_roi": 0.15
    }',
    '[
        {
            "description": "Single family home example",
            "arv": 150000,
            "repair_costs": 25000,
            "holding_costs": 2000,
            "assignment_fee": 5000,
            "calculated_mao": 73000
        }
    ]',
    'critical'
) ON CONFLICT (template_name) DO NOTHING;

-- Create view for document analytics
CREATE OR REPLACE VIEW vw_document_analytics AS
SELECT 
    kb.document_id,
    kb.source_identifier,
    kb.metadata->>'document_type' as document_type,
    kb.metadata->>'priority' as priority,
    COUNT(kb.id) as total_chunks,
    COUNT(DISTINCT kua.id) as usage_count,
    AVG(kua.effectiveness_score) as avg_effectiveness,
    MAX(kua.usage_timestamp) as last_used,
    kb.created_at as uploaded_at
FROM knowledge_base kb
LEFT JOIN knowledge_usage_analytics kua ON kb.document_id = kua.document_id
WHERE kb.document_id IS NOT NULL
GROUP BY kb.document_id, kb.source_identifier, kb.metadata, kb.created_at;

-- Create view for agent learning insights
CREATE OR REPLACE VIEW vw_agent_learning_insights AS
SELECT 
    agent_name,
    feedback_type,
    COUNT(*) as feedback_count,
    AVG(CASE 
        WHEN feedback_type = 'success' THEN 1.0
        WHEN feedback_type = 'improvement' THEN 0.5
        ELSE 0.0
    END) as success_rate,
    DATE_TRUNC('day', created_at) as feedback_date
FROM agent_learning_feedback
GROUP BY agent_name, feedback_type, DATE_TRUNC('day', created_at)
ORDER BY feedback_date DESC;

-- Create view for knowledge effectiveness
CREATE OR REPLACE VIEW vw_knowledge_effectiveness AS
SELECT 
    kua.usage_context,
    kua.agent_name,
    kb.metadata->>'document_type' as document_type,
    COUNT(*) as usage_count,
    AVG(kua.effectiveness_score) as avg_effectiveness,
    MAX(kua.usage_timestamp) as last_used
FROM knowledge_usage_analytics kua
JOIN knowledge_base kb ON kua.knowledge_chunk_id = kb.id
WHERE kua.effectiveness_score IS NOT NULL
GROUP BY kua.usage_context, kua.agent_name, kb.metadata->>'document_type'
ORDER BY avg_effectiveness DESC;

-- Add RLS policies for knowledge management tables
ALTER TABLE document_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_learning_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE knowledge_usage_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE deal_calculation_templates ENABLE ROW LEVEL SECURITY;

-- Create policies (adjust based on your authentication setup)
CREATE POLICY "Allow all operations for authenticated users" ON document_templates
    FOR ALL USING (true);

CREATE POLICY "Allow all operations for authenticated users" ON agent_learning_feedback
    FOR ALL USING (true);

CREATE POLICY "Allow all operations for authenticated users" ON knowledge_usage_analytics
    FOR ALL USING (true);

CREATE POLICY "Allow all operations for authenticated users" ON deal_calculation_templates
    FOR ALL USING (true);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_document_templates_updated_at 
    BEFORE UPDATE ON document_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_deal_calc_templates_updated_at 
    BEFORE UPDATE ON deal_calculation_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
