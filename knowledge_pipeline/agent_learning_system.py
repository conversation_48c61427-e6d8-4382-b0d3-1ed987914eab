"""
Agent Learning System
Captures agent performance, learns from outcomes, and improves decision-making
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from supabase import Client

logger = logging.getLogger(__name__)

@dataclass
class LearningFeedback:
    """Structure for agent learning feedback"""
    lead_id: str
    agent_name: str
    action_taken: str
    outcome: str
    feedback_type: str  # 'success', 'failure', 'improvement'
    feedback_data: Dict[str, Any]
    knowledge_applied: Optional[Dict[str, Any]] = None
    lessons_learned: Optional[Dict[str, Any]] = None

@dataclass
class KnowledgeUsage:
    """Structure for tracking knowledge usage"""
    document_id: str
    knowledge_chunk_id: str
    agent_name: str
    usage_context: str
    lead_id: Optional[str] = None
    effectiveness_score: Optional[float] = None
    usage_metadata: Optional[Dict[str, Any]] = None

class AgentLearningSystem:
    """System for capturing and applying agent learning"""
    
    def __init__(self, supabase_client: Client):
        self.supabase = supabase_client
        
    def record_agent_feedback(self, feedback: LearningFeedback) -> bool:
        """Record feedback from agent actions"""
        try:
            data = {
                "lead_id": feedback.lead_id,
                "agent_name": feedback.agent_name,
                "action_taken": feedback.action_taken,
                "outcome": feedback.outcome,
                "feedback_type": feedback.feedback_type,
                "feedback_data": feedback.feedback_data,
                "knowledge_applied": feedback.knowledge_applied or {},
                "lessons_learned": feedback.lessons_learned or {}
            }
            
            response = self.supabase.table("agent_learning_feedback").insert(data).execute()
            
            if response.data:
                logger.info(f"Recorded learning feedback for {feedback.agent_name}: {feedback.feedback_type}")
                return True
            else:
                logger.error(f"Failed to record learning feedback: {response}")
                return False
                
        except Exception as e:
            logger.error(f"Error recording agent feedback: {e}", exc_info=True)
            return False
    
    def record_knowledge_usage(self, usage: KnowledgeUsage) -> bool:
        """Record when and how knowledge is used"""
        try:
            data = {
                "document_id": usage.document_id,
                "knowledge_chunk_id": usage.knowledge_chunk_id,
                "agent_name": usage.agent_name,
                "usage_context": usage.usage_context,
                "lead_id": usage.lead_id,
                "effectiveness_score": usage.effectiveness_score,
                "usage_metadata": usage.usage_metadata or {}
            }
            
            response = self.supabase.table("knowledge_usage_analytics").insert(data).execute()
            
            if response.data:
                logger.info(f"Recorded knowledge usage for {usage.agent_name} in {usage.usage_context}")
                return True
            else:
                logger.error(f"Failed to record knowledge usage: {response}")
                return False
                
        except Exception as e:
            logger.error(f"Error recording knowledge usage: {e}", exc_info=True)
            return False
    
    def get_agent_performance_insights(self, agent_name: str, days: int = 30) -> Dict[str, Any]:
        """Get performance insights for a specific agent"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            response = self.supabase.table("agent_learning_feedback").select(
                "feedback_type, outcome, action_taken, feedback_data, created_at"
            ).eq("agent_name", agent_name).gte("created_at", cutoff_date).execute()
            
            if not response.data:
                return {"agent_name": agent_name, "insights": "No data available"}
            
            feedback_data = response.data
            total_actions = len(feedback_data)
            success_count = len([f for f in feedback_data if f["feedback_type"] == "success"])
            failure_count = len([f for f in feedback_data if f["feedback_type"] == "failure"])
            improvement_count = len([f for f in feedback_data if f["feedback_type"] == "improvement"])
            
            # Analyze common actions and outcomes
            action_outcomes = {}
            for feedback in feedback_data:
                action = feedback["action_taken"]
                outcome = feedback["outcome"]
                if action not in action_outcomes:
                    action_outcomes[action] = {"success": 0, "failure": 0, "improvement": 0}
                action_outcomes[action][feedback["feedback_type"]] += 1
            
            # Calculate success rates by action
            action_success_rates = {}
            for action, outcomes in action_outcomes.items():
                total = sum(outcomes.values())
                success_rate = (outcomes["success"] + outcomes["improvement"] * 0.5) / total if total > 0 else 0
                action_success_rates[action] = {
                    "success_rate": round(success_rate, 2),
                    "total_attempts": total,
                    "outcomes": outcomes
                }
            
            return {
                "agent_name": agent_name,
                "period_days": days,
                "total_actions": total_actions,
                "success_rate": round(success_count / total_actions, 2) if total_actions > 0 else 0,
                "feedback_breakdown": {
                    "success": success_count,
                    "failure": failure_count,
                    "improvement": improvement_count
                },
                "action_performance": action_success_rates,
                "insights": self._generate_performance_insights(action_success_rates, feedback_data)
            }
            
        except Exception as e:
            logger.error(f"Error getting agent performance insights: {e}", exc_info=True)
            return {"error": str(e)}
    
    def get_knowledge_effectiveness(self, document_type: Optional[str] = None, days: int = 30) -> Dict[str, Any]:
        """Get effectiveness metrics for knowledge base"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            # Build query
            query = self.supabase.table("knowledge_usage_analytics").select(
                "document_id, usage_context, agent_name, effectiveness_score, usage_timestamp"
            ).gte("usage_timestamp", cutoff_date)
            
            if document_type:
                # Join with knowledge_base to filter by document type
                query = self.supabase.from_("vw_knowledge_effectiveness").select("*")
                if document_type:
                    query = query.eq("document_type", document_type)
            
            response = query.execute()
            
            if not response.data:
                return {"effectiveness": "No usage data available"}
            
            usage_data = response.data
            
            # Calculate overall effectiveness
            effectiveness_scores = [u["effectiveness_score"] for u in usage_data if u.get("effectiveness_score")]
            avg_effectiveness = sum(effectiveness_scores) / len(effectiveness_scores) if effectiveness_scores else 0
            
            # Group by context
            context_effectiveness = {}
            for usage in usage_data:
                context = usage["usage_context"]
                if context not in context_effectiveness:
                    context_effectiveness[context] = []
                if usage.get("effectiveness_score"):
                    context_effectiveness[context].append(usage["effectiveness_score"])
            
            # Calculate averages by context
            context_averages = {}
            for context, scores in context_effectiveness.items():
                context_averages[context] = {
                    "avg_effectiveness": round(sum(scores) / len(scores), 2) if scores else 0,
                    "usage_count": len(scores)
                }
            
            return {
                "period_days": days,
                "overall_effectiveness": round(avg_effectiveness, 2),
                "total_usages": len(usage_data),
                "context_effectiveness": context_averages,
                "recommendations": self._generate_knowledge_recommendations(context_averages)
            }
            
        except Exception as e:
            logger.error(f"Error getting knowledge effectiveness: {e}", exc_info=True)
            return {"error": str(e)}
    
    def get_learning_recommendations(self, agent_name: str) -> List[Dict[str, Any]]:
        """Get personalized learning recommendations for an agent"""
        try:
            # Get recent performance data
            performance = self.get_agent_performance_insights(agent_name, days=14)
            
            recommendations = []
            
            if "action_performance" in performance:
                # Identify low-performing actions
                for action, metrics in performance["action_performance"].items():
                    if metrics["success_rate"] < 0.6 and metrics["total_attempts"] >= 3:
                        recommendations.append({
                            "type": "improvement",
                            "priority": "high",
                            "action": action,
                            "current_success_rate": metrics["success_rate"],
                            "suggestion": f"Review and improve {action} strategy. Current success rate is {metrics['success_rate']*100:.0f}%",
                            "knowledge_needed": self._suggest_knowledge_for_action(action)
                        })
                
                # Identify successful patterns to reinforce
                for action, metrics in performance["action_performance"].items():
                    if metrics["success_rate"] > 0.8 and metrics["total_attempts"] >= 3:
                        recommendations.append({
                            "type": "reinforcement",
                            "priority": "medium",
                            "action": action,
                            "current_success_rate": metrics["success_rate"],
                            "suggestion": f"Excellent performance on {action}. Consider documenting this approach for other agents.",
                            "knowledge_needed": []
                        })
            
            # Add general recommendations based on overall performance
            if performance.get("success_rate", 0) < 0.7:
                recommendations.append({
                    "type": "general",
                    "priority": "high",
                    "suggestion": "Overall performance below target. Consider additional training on deal analysis fundamentals.",
                    "knowledge_needed": ["deal_analysis", "mao_calculation", "negotiation_strategy"]
                })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating learning recommendations: {e}", exc_info=True)
            return []
    
    def _generate_performance_insights(self, action_performance: Dict, feedback_data: List) -> List[str]:
        """Generate insights from performance data"""
        insights = []
        
        # Find best and worst performing actions
        if action_performance:
            best_action = max(action_performance.items(), key=lambda x: x[1]["success_rate"])
            worst_action = min(action_performance.items(), key=lambda x: x[1]["success_rate"])
            
            insights.append(f"Best performing action: {best_action[0]} ({best_action[1]['success_rate']*100:.0f}% success rate)")
            
            if worst_action[1]["success_rate"] < 0.5:
                insights.append(f"Needs improvement: {worst_action[0]} ({worst_action[1]['success_rate']*100:.0f}% success rate)")
        
        # Analyze trends
        recent_feedback = sorted(feedback_data, key=lambda x: x["created_at"])[-10:]
        recent_success_rate = len([f for f in recent_feedback if f["feedback_type"] == "success"]) / len(recent_feedback) if recent_feedback else 0
        
        if recent_success_rate > 0.7:
            insights.append("Recent performance is strong - trending upward")
        elif recent_success_rate < 0.4:
            insights.append("Recent performance needs attention - consider additional training")
        
        return insights
    
    def _generate_knowledge_recommendations(self, context_effectiveness: Dict) -> List[str]:
        """Generate recommendations for knowledge improvement"""
        recommendations = []
        
        for context, metrics in context_effectiveness.items():
            if metrics["avg_effectiveness"] < 0.6:
                recommendations.append(f"Improve knowledge for {context} - current effectiveness: {metrics['avg_effectiveness']*100:.0f}%")
            elif metrics["avg_effectiveness"] > 0.8:
                recommendations.append(f"Excellent knowledge effectiveness for {context} - consider expanding this knowledge area")
        
        return recommendations
    
    def _suggest_knowledge_for_action(self, action: str) -> List[str]:
        """Suggest knowledge types that might help with specific actions"""
        knowledge_mapping = {
            "calculate_mao": ["mao_calculation", "deal_analysis"],
            "analyze_deal": ["deal_analysis", "market_analysis"],
            "negotiate_price": ["negotiation_strategy", "market_analysis"],
            "evaluate_property": ["deal_analysis", "market_analysis"],
            "make_offer": ["mao_calculation", "negotiation_strategy"],
            "follow_up": ["follow_up_sequence", "negotiation_strategy"]
        }
        
        return knowledge_mapping.get(action.lower(), ["general_knowledge"])

# Global learning system instance (will be initialized with supabase client)
learning_system: Optional[AgentLearningSystem] = None

def initialize_learning_system(supabase_client: Client):
    """Initialize the global learning system"""
    global learning_system
    learning_system = AgentLearningSystem(supabase_client)
    logger.info("Agent learning system initialized")

def record_agent_action(lead_id: str, agent_name: str, action: str, outcome: str, 
                       success: bool, details: Dict[str, Any], knowledge_used: Optional[Dict] = None):
    """Convenience function to record agent actions"""
    if not learning_system:
        logger.warning("Learning system not initialized - cannot record feedback")
        return False
    
    feedback_type = "success" if success else "failure"
    
    feedback = LearningFeedback(
        lead_id=lead_id,
        agent_name=agent_name,
        action_taken=action,
        outcome=outcome,
        feedback_type=feedback_type,
        feedback_data=details,
        knowledge_applied=knowledge_used
    )
    
    return learning_system.record_agent_feedback(feedback)
