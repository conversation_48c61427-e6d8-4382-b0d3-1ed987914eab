import os
import logging
import json
import hashlib
from typing import List, Dict, <PERSON>, Tu<PERSON>, Optional
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client, Client
import openai # openai >= 1.0.0
from knowledge_pipeline.utils.contextual_summary import generate_contextual_summary

# Imports for consolidated utility functions
import subprocess
import requests
from bs4 import BeautifulSoup

# Import deal analysis processor
try:
    from knowledge_pipeline.deal_analysis_processor import deal_processor
except ImportError:
    logging.warning("Deal analysis processor not available. Structured analysis will be skipped.")
    deal_processor = None

# Define custom exception
class IngestionError(Exception):
    """Custom exception for ingestion pipeline errors."""
    pass

# Load environment variables
# Refined logic to find .env by looking for a project root marker
try:
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root_marker = "requirements.txt"  # A common file at the project root

    # Traverse up to find the project root (max 3 levels up from current file's dir)
    temp_path = current_dir
    project_root = None
    for _ in range(4): # Check current dir and up to 3 parent dirs
        # Check if marker exists in the current temp_path
        # For files like requirements.txt, check if it exists in temp_path
        if os.path.exists(os.path.join(temp_path, project_root_marker)):
            project_root = temp_path
            break
        parent_path = os.path.dirname(temp_path)
        if parent_path == temp_path:  # Reached filesystem root
            break
        temp_path = parent_path
    
    if project_root:
        dotenv_path = os.path.join(project_root, ".env")
        if os.path.exists(dotenv_path):
            load_dotenv(dotenv_path)
            logging.info(f"Attempted to load .env from: {dotenv_path}")
        else:
            logging.info(f".env file not found at presumed project root: {project_root}. Attempting default load.")
            load_dotenv()  # Fallback to default search paths for dotenv
    else:
        # If project root couldn't be determined, try loading from current/default paths
        logging.info("Could not determine project root to load .env. Falling back to default dotenv search.")
        load_dotenv()
except Exception as e:
    logging.warning(f"Error during .env loading: {e}. Proceeding without .env if not already loaded.")


# Environment variables
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")
SUPABASE_TABLE_NAME = os.getenv("SUPABASE_KB_TABLE_NAME", "kb_chunks")

# Initialize logger
if not logging.getLogger().hasHandlers():  # Avoid reconfiguring if already set
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# Initialize OpenAI client
openai_client: Optional[openai.OpenAI] = None
if OPENAI_API_KEY:
    try:
        openai_client = openai.OpenAI(api_key=OPENAI_API_KEY)
        logging.info("OpenAI client initialized successfully.")
    except Exception as e:
        logging.error(f"Failed to initialize OpenAI client: {e}")
else:
    logging.error("OPENAI_API_KEY not found in environment variables. Embedding generation will fail.")

# Global Supabase client (primarily for __main__ block or if not overridden)
_global_supabase_client: Optional[Client] = None
if SUPABASE_URL and SUPABASE_KEY:
    try:
        _global_supabase_client = create_client(SUPABASE_URL, SUPABASE_KEY)
        logging.info("Global Supabase client initialized successfully.")
    except Exception as e:
        logging.error(f"Failed to initialize global Supabase client: {e}")
else:
    logging.error("Supabase URL or Key not found for global client. Operations in __main__ might fail.")

DEFAULT_CHUNK_SIZE = int(os.getenv("DEFAULT_CHUNK_SIZE", 500))
DEFAULT_OVERLAP = int(os.getenv("DEFAULT_OVERLAP", 50))
EMBEDDING_MODEL = "text-embedding-ada-002"

# --- Consolidated Utility Functions ---

def extract_text_from_pdf(file_path: str) -> str:
    try:
        process = subprocess.run(
            ["pdftotext", file_path, "-"], 
            capture_output=True, 
            text=True, 
            check=True,
            encoding='utf-8' # Specify encoding
        )
        return process.stdout
    except subprocess.CalledProcessError as e:
        logging.error(f"Error extracting text from PDF {file_path} with pdftotext: {e.stderr}")
        raise IngestionError(f"pdftotext failed for {file_path}: {e.stderr}") from e
    except FileNotFoundError:
        logging.error("pdftotext command not found. Ensure poppler-utils is installed and in PATH.")
        raise IngestionError("pdftotext command not found. Is poppler-utils installed and in PATH?")
    except Exception as e:
        logging.error(f"Unexpected error extracting text from PDF {file_path}: {e}")
        raise IngestionError(f"Unexpected error with PDF {file_path}: {e}") from e

def extract_text_from_txt(file_path: str) -> str:
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        logging.error(f"Error reading text file {file_path}: {e}")
        raise IngestionError(f"Error reading text file {file_path}: {e}") from e

def extract_text_from_md(file_path: str) -> str:
    return extract_text_from_txt(file_path)

def extract_text_from_csv(file_path: str) -> str:
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            # Consider using csv module for more robust parsing if needed
            return f.read()
    except Exception as e:
        logging.error(f"Error reading CSV file {file_path}: {e}")
        raise IngestionError(f"Error reading CSV file {file_path}: {e}") from e

def extract_text_from_url(url: str) -> str:
    try:
        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}
        response = requests.get(url, timeout=20, headers=headers)
        response.raise_for_status()
        
        content_type = response.headers.get('content-type', '').lower()
        
        if 'html' in content_type:
            soup = BeautifulSoup(response.content, "html.parser")
            for script_or_style in soup(["script", "style", "header", "footer", "nav", "aside", "form", "button", "input"]):
                script_or_style.decompose()
            text = soup.get_text(separator='\n', strip=True)
            text = '\n'.join(line.strip() for line in text.splitlines() if line.strip())
            return text
        elif 'text/plain' in content_type:
            return response.text
        else:
            logging.warning(f"Unsupported content type '{content_type}' for URL {url}. Attempting to decode as text.")
            return response.text
    except requests.exceptions.RequestException as e:
        logging.error(f"Error fetching URL {url}: {e}")
        raise IngestionError(f"Error fetching URL {url}: {e}") from e
    except Exception as e:
        logging.error(f"Error parsing content from URL {url}: {e}")
        raise IngestionError(f"Error parsing content from URL {url}: {e}") from e

def get_text_content(source_path: str, source_type: str) -> Tuple[str, Dict[str, Any]]:
    raw_text = ""
    metadata: Dict[str, Any] = {"source": source_path, "source_type": source_type, "processed_at": datetime.utcnow().isoformat()}

    if source_type == "file_upload":
        if not os.path.exists(source_path):
            raise IngestionError(f"File not found: {source_path}")
        
        file_extension = os.path.splitext(source_path)[1].lower()
        metadata["filename"] = os.path.basename(source_path)
        metadata["file_extension"] = file_extension
        metadata["file_size_bytes"] = os.path.getsize(source_path)
        
        if file_extension == ".pdf":
            raw_text = extract_text_from_pdf(source_path)
        elif file_extension == ".txt":
            raw_text = extract_text_from_txt(source_path)
        elif file_extension == ".md":
            raw_text = extract_text_from_md(source_path)
        elif file_extension == ".csv":
            raw_text = extract_text_from_csv(source_path)
        else:
            raise IngestionError(f"Unsupported file type: {file_extension} for {source_path}")
            
    elif source_type == "url_crawl":
        raw_text = extract_text_from_url(source_path)
        metadata["url"] = source_path
    else:
        raise IngestionError(f"Unknown source type: {source_type}")
        
    return raw_text, metadata

def chunk_text(text: str, chunk_size: int, overlap: int) -> list[str]:
    if not text:
        return []
    # Simple character-based chunking
    chunks = []
    start_index = 0
    text_length = len(text)
    while start_index < text_length:
        end_index = min(start_index + chunk_size, text_length)
        chunks.append(text[start_index:end_index])
        start_index += (chunk_size - overlap)
        if start_index >= text_length and end_index < text_length : # ensure last part is captured if overlap makes start_index skip it
             if text[end_index-overlap:] not in chunks[-1] and end_index-overlap < text_length : # avoid re-adding if overlap covers it
                 # This logic for last chunk might need refinement based on exact needs
                 pass # Current simple chunking might miss a very small last segment if overlap is large.
                      # For now, keeping it simple. A text_splitter library would handle this better.
    return [chunk for chunk in chunks if chunk.strip()]


def get_embedding(text_chunk: str, model: str = EMBEDDING_MODEL) -> Optional[List[float]]:
    if not openai_client:
        logging.error("OpenAI client not initialized. Cannot generate embedding.")
        return None
    try:
        # OpenAI API expects a list of strings for input, even for a single chunk
        response = openai_client.embeddings.create(input=[text_chunk.replace("\n", " ")], model=model)
        return response.data[0].embedding
    except Exception as e:
        logging.error(f"Error generating single embedding: {e}", exc_info=True)
        return None

def batch_get_embeddings(text_chunks: List[str], model: str = EMBEDDING_MODEL) -> List[Optional[List[float]]]:
    if not openai_client:
        logging.error("OpenAI client not initialized. Cannot generate batch embeddings.")
        return [None] * len(text_chunks)
    
    embeddings: List[Optional[List[float]]] = []
    try:
        # Process chunks in batches if necessary, though OpenAI API might handle large inputs
        # For simplicity, sending all at once. Max tokens per request for embeddings API should be checked.
        # Current model text-embedding-ada-002 supports up to 8191 tokens per input array element.
        # And up to 2048 elements in the input array.
        processed_chunks = [chunk.replace("\n", " ") for chunk in text_chunks]
        response = openai_client.embeddings.create(input=processed_chunks, model=model)
        
        # Ensure the number of embeddings matches the number of input chunks
        if len(response.data) == len(text_chunks):
            embeddings = [item.embedding for item in response.data]
        else:
            logging.error(f"Mismatched count of embeddings ({len(response.data)}) and text chunks ({len(text_chunks)}).")
            embeddings = [None] * len(text_chunks) # Fill with None if mismatch

    except Exception as e:
        logging.error(f"Error generating batch embeddings: {e}", exc_info=True)
        embeddings = [None] * len(text_chunks) # Fill with None on error
        
    return embeddings

# --- Main Processing Logic ---

def insert_into_supabase(supabase_client: Client, data_to_insert: list[dict]) -> bool:
    if not supabase_client:
        logging.error("Supabase client not initialized. Cannot insert data.")
        return False
    if not data_to_insert:
        logging.info("No data to insert into Supabase.")
        return True
    try:
        response = supabase_client.table(SUPABASE_TABLE_NAME).insert(data_to_insert).execute()
        if hasattr(response, 'data') and response.data:
            logging.info(f"Successfully inserted {len(response.data)} records into Supabase.")
            return True
        elif hasattr(response, 'error') and response.error:
            logging.error(f"Supabase insertion failed. Error: {response.error}")
            return False
        else:
            logging.error(f"Supabase insertion failed or returned no data. Response: {response}")
            return False
    except Exception as e:
        logging.error(f"Error inserting data into Supabase: {e}")
        raise IngestionError(f"Supabase insertion error: {e}") from e

def process_document(
    supabase_client: Client,
    source_identifier: str,
    source_type: str,
    file_path: str | None = None,
    tags: list[str] | None = None,
    chunk_size_override: int | None = None,
    overlap_override: int | None = None,
    location_filter: str | None = None,
    user_metadata: dict | None = None
) -> tuple[str, int]:
    logging.info(f"Starting processing for {source_type}: {source_identifier}")
    actual_chunk_size = chunk_size_override if chunk_size_override is not None else DEFAULT_CHUNK_SIZE
    actual_overlap = overlap_override if overlap_override is not None else DEFAULT_OVERLAP
    
    document_id_to_return = source_identifier
    inserted_chunks_count = 0

    raw_text = ""
    initial_doc_metadata: Dict[str, Any] = {}

    try:
        if source_type == "file_upload" and file_path:
            raw_text, initial_doc_metadata = get_text_content(file_path, source_type)
        elif source_type == "url_crawl":
            raw_text, initial_doc_metadata = get_text_content(source_identifier, source_type)
        else:
            msg = f"Invalid source_type ('{source_type}') or missing file_path for file_upload."
            logging.error(msg)
            raise IngestionError(msg)

        if not raw_text or not raw_text.strip():
            logging.warning(f"No text content extracted from {source_identifier}. Skipping.")
            return document_id_to_return, 0

        final_doc_metadata = initial_doc_metadata.copy()
        if user_metadata:
            final_doc_metadata.update(user_metadata)
        if tags:
            final_doc_metadata["document_tags"] = tags

        # Process deal analysis documents for structured knowledge extraction
        structured_knowledge = None
        if deal_processor and user_metadata:
            try:
                structured_knowledge = deal_processor.process_deal_document(raw_text, user_metadata)
                final_doc_metadata["structured_knowledge"] = structured_knowledge
                logging.info(f"Extracted structured knowledge for {source_identifier}: {structured_knowledge.get('type', 'unknown')}")
            except Exception as e:
                logging.warning(f"Failed to extract structured knowledge from {source_identifier}: {e}")

        # Generate document ID based on content and metadata
        content_hash = hashlib.sha256(raw_text.encode('utf-8')).hexdigest()[:16]
        document_id = f"{user_metadata.get('document_type', 'general')}_{content_hash}"
        final_doc_metadata["document_id"] = document_id

        text_chunks = chunk_text(raw_text, actual_chunk_size, actual_overlap)
        if not text_chunks:
            logging.warning(f"No chunks created from {source_identifier}. Skipping.")
            return document_id_to_return, 0
        logging.info(f"Created {len(text_chunks)} chunks for {source_identifier}.")

        summarized_chunks = []
        for i, chunk_content in enumerate(text_chunks):
            try:
                summary = generate_contextual_summary(source_identifier, raw_text, chunk_content, chunk_idx=i, total_chunks=len(text_chunks))
                summarized_chunk_text = f"{summary.strip()}\n---\n{chunk_content.strip()}"
                summarized_chunks.append(summarized_chunk_text)
            except Exception as e:
                logging.warning(f"Failed to generate summary for chunk {i} of {source_identifier}: {e}. Using raw chunk.")
                summarized_chunks.append(chunk_content.strip())

        chunk_embeddings = batch_get_embeddings(summarized_chunks)
        
        data_to_insert = []
        for i, chunk_text_content_with_summary in enumerate(summarized_chunks):
            # Ensure we have a valid embedding for this chunk
            if i < len(chunk_embeddings) and chunk_embeddings[i] is not None:
                embedding = chunk_embeddings[i]
            else:
                logging.warning(f"Skipping chunk {i+1} for {source_identifier} due to missing or failed embedding.")
                continue # Skip this chunk
            
            chunk_hash = hashlib.sha256(chunk_text_content_with_summary.encode('utf-8')).hexdigest()
            
            if supabase_client:
                try:
                    existing_response = supabase_client.table(SUPABASE_TABLE_NAME).select("chunk_hash").eq("chunk_hash", chunk_hash).limit(1).execute()
                    if existing_response.data:
                        logging.info(f"Duplicate chunk detected (hash: {chunk_hash}) for {source_identifier}, skipping.")
                        continue
                except Exception as e:
                    logging.warning(f"Error checking for existing chunk hash {chunk_hash} for {source_identifier}: {e}")
            
            chunk_specific_metadata = final_doc_metadata.copy() 
            chunk_specific_metadata["chunk_index"] = i
            chunk_specific_metadata["total_chunks_in_document"] = len(text_chunks)

            record = {
                "source_identifier": source_identifier, 
                "content": chunk_text_content_with_summary,
                "embedding": embedding, # This is now guaranteed to be non-None
                "tags": tags if tags else [], 
                "chunk_size": actual_chunk_size,
                "overlap": actual_overlap,
                "upload_method": source_type,
                "location_filter": location_filter, 
                "metadata": chunk_specific_metadata, 
                "chunk_hash": chunk_hash,
            }
            data_to_insert.append(record)

        if data_to_insert:
            if insert_into_supabase(supabase_client, data_to_insert):
                inserted_chunks_count = len(data_to_insert)
            else:
                raise IngestionError(f"Failed to insert chunks for {document_id_to_return} into Supabase.")
        else:
            logging.warning(f"No new, unique, or successfully embedded data prepared for Supabase insertion for {source_identifier}.")
            inserted_chunks_count = 0
        
        logging.info(f"Finished processing for {source_identifier}. Chunks inserted: {inserted_chunks_count}")
        return document_id_to_return, inserted_chunks_count

    except IngestionError as e:
        logging.error(f"IngestionError during processing of {source_identifier}: {e}")
        raise
    except Exception as e:
        logging.error(f"Unexpected error during processing of {source_identifier}: {e}", exc_info=True)
        raise IngestionError(f"Unexpected error processing {source_identifier}: {e}") from e

def process_document_from_file(
    filepath: str, 
    filename: str, 
    supabase_client: Client, 
    tags: list[str] | None = None, 
    chunk_size: int | None = None, 
    overlap: int | None = None, 
    location_filter: str | None = None, 
    user_metadata: dict | None = None
) -> tuple[str, int]:
    if not supabase_client:
        raise IngestionError("Supabase client must be provided for file processing.")
    return process_document(
        supabase_client=supabase_client,
        source_identifier=filename,
        source_type="file_upload",
        file_path=filepath,
        tags=tags,
        chunk_size_override=chunk_size,
        overlap_override=overlap,
        location_filter=location_filter,
        user_metadata=user_metadata
    )

def process_document_from_url(
    url: str, 
    supabase_client: Client, 
    tags: list[str] | None = None, 
    chunk_size: int | None = None, 
    overlap: int | None = None, 
    location_filter: str | None = None, 
    user_metadata: dict | None = None
) -> tuple[str, int]:
    if not supabase_client:
        raise IngestionError("Supabase client must be provided for URL processing.")
    return process_document(
        supabase_client=supabase_client,
        source_identifier=url,
        source_type="url_crawl",
        file_path=None,
        tags=tags,
        chunk_size_override=chunk_size,
        overlap_override=overlap,
        location_filter=location_filter,
        user_metadata=user_metadata
    )

if __name__ == "__main__":
    logging.info("Ingestion pipeline script started (test mode).")
    if _global_supabase_client:
        test_file_name = "test_document.txt"
        with open(test_file_name, "w", encoding="utf-8") as f:
            f.write("This is a test document for the ingestion pipeline. " * 20)
            f.write("It contains multiple sentences and should be chunked. " * 20)
            f.write("Another sentence to ensure enough content for several chunks." * 20)

        try:
            doc_id, count = process_document_from_file(
                filepath=test_file_name,
                filename=test_file_name,
                supabase_client=_global_supabase_client,
                tags=["test", "example"],
                user_metadata={"category": "testing"}
            )
            logging.info(f"Test file processing result: document_id='{doc_id}', chunks_inserted={count}")
        except IngestionError as e:
            logging.error(f"Test file processing failed: {e}")
        finally:
            if os.path.exists(test_file_name):
                os.remove(test_file_name)
    else:
        logging.error("Global Supabase client not initialized. Cannot run __main__ tests.")

