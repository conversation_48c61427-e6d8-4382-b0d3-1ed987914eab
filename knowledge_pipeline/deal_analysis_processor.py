"""
Deal Analysis Document Processor
Specialized processor for deal analysis templates, MAO calculations, and investment strategies
"""

import json
import re
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class DealAnalysisTemplate:
    """Structure for deal analysis templates"""
    name: str
    description: str
    calculation_steps: List[str]
    formulas: Dict[str, str]
    variables: Dict[str, Any]
    decision_criteria: Dict[str, Any]
    examples: List[Dict[str, Any]]

class DealAnalysisProcessor:
    """Processes deal analysis documents and extracts structured knowledge"""
    
    def __init__(self):
        self.formula_patterns = {
            'mao': r'MAO\s*=\s*([^;\n]+)',
            'arv': r'ARV\s*=\s*([^;\n]+)',
            'repair_costs': r'(?:Repair|Rehab)\s*(?:Costs?)?\s*=\s*([^;\n]+)',
            'profit_margin': r'(?:Profit|Margin)\s*=\s*([^;\n]+)',
            'holding_costs': r'Holding\s*Costs?\s*=\s*([^;\n]+)',
            'closing_costs': r'Closing\s*Costs?\s*=\s*([^;\n]+)'
        }
        
        self.criteria_patterns = {
            'min_profit': r'(?:Minimum|Min)\s*Profit\s*[:=]\s*\$?([0-9,]+)',
            'max_repair': r'(?:Maximum|Max)\s*Repair\s*[:=]\s*\$?([0-9,]+)',
            'target_margin': r'Target\s*Margin\s*[:=]\s*([0-9.]+)%?'
        }

    def process_deal_document(self, content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a deal analysis document and extract structured knowledge
        
        Args:
            content: Raw document content
            metadata: Document metadata
            
        Returns:
            Structured deal analysis data
        """
        doc_type = metadata.get('document_type', 'general_knowledge')
        
        if doc_type == 'mao_calculation':
            return self._process_mao_document(content, metadata)
        elif doc_type == 'deal_analysis':
            return self._process_deal_analysis_template(content, metadata)
        elif doc_type == 'market_analysis':
            return self._process_market_analysis(content, metadata)
        elif doc_type == 'negotiation_strategy':
            return self._process_negotiation_strategy(content, metadata)
        else:
            return self._process_general_document(content, metadata)

    def _process_mao_document(self, content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Process MAO calculation documents"""
        logger.info("Processing MAO calculation document")
        
        # Extract formulas
        formulas = {}
        for formula_type, pattern in self.formula_patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                formulas[formula_type] = matches[0].strip()
        
        # Extract decision criteria
        criteria = {}
        for criteria_type, pattern in self.criteria_patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                criteria[criteria_type] = matches[0].replace(',', '')
        
        # Extract calculation steps
        steps = self._extract_calculation_steps(content)
        
        # Extract examples
        examples = self._extract_examples(content)
        
        return {
            'type': 'mao_calculation',
            'formulas': formulas,
            'criteria': criteria,
            'calculation_steps': steps,
            'examples': examples,
            'priority': metadata.get('priority', 'high'),
            'tags': metadata.get('document_tags', [])
        }

    def _process_deal_analysis_template(self, content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Process general deal analysis templates"""
        logger.info("Processing deal analysis template")
        
        # Extract sections
        sections = self._extract_sections(content)
        
        # Extract checklists
        checklists = self._extract_checklists(content)
        
        # Extract decision trees
        decision_points = self._extract_decision_points(content)
        
        return {
            'type': 'deal_analysis_template',
            'sections': sections,
            'checklists': checklists,
            'decision_points': decision_points,
            'priority': metadata.get('priority', 'medium'),
            'tags': metadata.get('document_tags', [])
        }

    def _process_market_analysis(self, content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Process market analysis documents"""
        logger.info("Processing market analysis document")
        
        # Extract market indicators
        indicators = self._extract_market_indicators(content)
        
        # Extract location criteria
        location_criteria = self._extract_location_criteria(content)
        
        return {
            'type': 'market_analysis',
            'market_indicators': indicators,
            'location_criteria': location_criteria,
            'priority': metadata.get('priority', 'medium'),
            'tags': metadata.get('document_tags', [])
        }

    def _process_negotiation_strategy(self, content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Process negotiation strategy documents"""
        logger.info("Processing negotiation strategy document")
        
        # Extract negotiation tactics
        tactics = self._extract_negotiation_tactics(content)
        
        # Extract objection handling
        objections = self._extract_objection_handling(content)
        
        return {
            'type': 'negotiation_strategy',
            'tactics': tactics,
            'objection_handling': objections,
            'priority': metadata.get('priority', 'high'),
            'tags': metadata.get('document_tags', [])
        }

    def _process_general_document(self, content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Process general knowledge documents"""
        return {
            'type': 'general_knowledge',
            'content_summary': content[:500] + "..." if len(content) > 500 else content,
            'priority': metadata.get('priority', 'low'),
            'tags': metadata.get('document_tags', [])
        }

    def _extract_calculation_steps(self, content: str) -> List[str]:
        """Extract numbered calculation steps"""
        steps = []
        step_patterns = [
            r'(?:Step\s*)?(\d+)[\.\)]\s*([^\n]+)',
            r'(\d+)\.\s*([^\n]+)',
            r'•\s*([^\n]+)',
            r'-\s*([^\n]+)'
        ]
        
        for pattern in step_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
            if matches:
                if len(matches[0]) == 2:  # Numbered steps
                    steps.extend([match[1].strip() for match in matches])
                else:  # Bullet points
                    steps.extend([match.strip() for match in matches])
                break
        
        return steps[:10]  # Limit to 10 steps

    def _extract_examples(self, content: str) -> List[Dict[str, Any]]:
        """Extract calculation examples"""
        examples = []
        
        # Look for example sections
        example_pattern = r'(?:Example|Sample|Case Study)[\s\d]*:?\s*([^(?:Example|Sample|Case Study)]+)'
        matches = re.findall(example_pattern, content, re.IGNORECASE | re.DOTALL)
        
        for match in matches[:3]:  # Limit to 3 examples
            example_text = match.strip()
            
            # Extract numbers from example
            numbers = re.findall(r'\$?([0-9,]+(?:\.[0-9]{2})?)', example_text)
            
            examples.append({
                'description': example_text[:200] + "..." if len(example_text) > 200 else example_text,
                'extracted_values': [num.replace(',', '') for num in numbers]
            })
        
        return examples

    def _extract_sections(self, content: str) -> Dict[str, str]:
        """Extract document sections"""
        sections = {}
        
        # Common section headers
        section_patterns = [
            r'(?:^|\n)((?:Property Analysis|Market Analysis|Financial Analysis|Risk Assessment)[^\n]*)\n([^(?:Property Analysis|Market Analysis|Financial Analysis|Risk Assessment)]+)',
            r'(?:^|\n)(#{1,3}\s*[^\n]+)\n([^#]+)'
        ]
        
        for pattern in section_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE | re.DOTALL)
            for header, content_section in matches:
                clean_header = re.sub(r'[#\*\-\s]+', ' ', header).strip()
                sections[clean_header] = content_section.strip()[:500]
        
        return sections

    def _extract_checklists(self, content: str) -> List[str]:
        """Extract checklists from content"""
        checklist_items = []
        
        # Look for checkbox patterns
        patterns = [
            r'☐\s*([^\n]+)',
            r'\[\s*\]\s*([^\n]+)',
            r'•\s*([^\n]+)',
            r'-\s*([^\n]+)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content)
            if matches:
                checklist_items.extend([item.strip() for item in matches])
                break
        
        return checklist_items[:15]  # Limit to 15 items

    def _extract_decision_points(self, content: str) -> List[Dict[str, str]]:
        """Extract decision points and criteria"""
        decision_points = []
        
        # Look for if/then patterns
        if_then_pattern = r'(?:If|When)\s+([^,\n]+),?\s*(?:then|do|perform)\s*([^\n]+)'
        matches = re.findall(if_then_pattern, content, re.IGNORECASE)
        
        for condition, action in matches[:10]:
            decision_points.append({
                'condition': condition.strip(),
                'action': action.strip()
            })
        
        return decision_points

    def _extract_market_indicators(self, content: str) -> Dict[str, Any]:
        """Extract market indicators and metrics"""
        indicators = {}
        
        # Common market indicators
        indicator_patterns = {
            'median_price': r'Median\s*(?:Home\s*)?Price\s*[:=]\s*\$?([0-9,]+)',
            'days_on_market': r'(?:Days?\s*on\s*Market|DOM)\s*[:=]\s*([0-9]+)',
            'inventory_months': r'(?:Months?\s*of\s*)?Inventory\s*[:=]\s*([0-9.]+)',
            'price_per_sqft': r'Price\s*per\s*(?:sq\.?\s*ft\.?|square\s*foot)\s*[:=]\s*\$?([0-9.]+)'
        }
        
        for indicator, pattern in indicator_patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                indicators[indicator] = matches[0].replace(',', '')
        
        return indicators

    def _extract_location_criteria(self, content: str) -> List[str]:
        """Extract location-specific criteria"""
        criteria = []
        
        location_patterns = [
            r'(?:Good|Desirable|Target)\s*(?:Areas?|Neighborhoods?|Locations?)\s*[:=]\s*([^\n]+)',
            r'(?:Avoid|Bad|Poor)\s*(?:Areas?|Neighborhoods?|Locations?)\s*[:=]\s*([^\n]+)',
            r'School\s*(?:District|Rating)\s*[:=]\s*([^\n]+)',
            r'Crime\s*(?:Rate|Level)\s*[:=]\s*([^\n]+)'
        ]
        
        for pattern in location_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            criteria.extend([match.strip() for match in matches])
        
        return criteria[:10]

    def _extract_negotiation_tactics(self, content: str) -> List[Dict[str, str]]:
        """Extract negotiation tactics and strategies"""
        tactics = []
        
        # Look for tactic descriptions
        tactic_pattern = r'(?:Tactic|Strategy|Approach)\s*\d*\s*[:=]\s*([^\n]+(?:\n(?!\s*(?:Tactic|Strategy|Approach))[^\n]+)*)'
        matches = re.findall(tactic_pattern, content, re.IGNORECASE | re.MULTILINE)
        
        for match in matches[:8]:
            tactics.append({
                'description': match.strip()[:300]
            })
        
        return tactics

    def _extract_objection_handling(self, content: str) -> List[Dict[str, str]]:
        """Extract objection handling responses"""
        objections = []
        
        # Look for objection/response patterns
        objection_pattern = r'(?:Objection|Q|Question)\s*[:=]\s*([^\n]+)\s*(?:Response|A|Answer)\s*[:=]\s*([^\n]+(?:\n(?!\s*(?:Objection|Q|Question))[^\n]+)*)'
        matches = re.findall(objection_pattern, content, re.IGNORECASE | re.MULTILINE)
        
        for objection, response in matches[:10]:
            objections.append({
                'objection': objection.strip(),
                'response': response.strip()[:300]
            })
        
        return objections

# Global processor instance
deal_processor = DealAnalysisProcessor()
