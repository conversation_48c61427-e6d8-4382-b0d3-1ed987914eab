
import os
import logging
import json
from functools import wraps
from datetime import datetime # Added for health check timestamp
from flask import Blueprint, request, jsonify, send_from_directory, abort
from werkzeug.utils import secure_filename
from werkzeug.exceptions import BadRequest, UnsupportedMediaType, InternalServerError, NotFound, Unauthorized, Conflict # Added more specific exceptions
from supabase import create_client, Client as SupabaseClient # type: ignore
from supabase.lib.client_options import ClientOptions # type: ignore
# Assuming these are for more specific error handling if ever implemented, not strictly used by current generic handlers
# from gotrue.errors import AuthApiError
# from postgrest.exceptions import APIError as PostgrestAPIError
# from storage3.utils import StorageException

# Assuming these local imports are correct relative to this file's location in the package
from .ingestion_pipeline import process_document_from_file, process_document_from_url, IngestionError
    
# Initialize logger
logger = logging.getLogger(__name__)
# BasicConfig should ideally be called once in the main application entry point.
# If this module is imported, this might reconfigure logging.
# For a blueprint, it's better if the main app handles logging config.
# However, to keep it self-contained for now as per original structure:
if not logger.hasHandlers(): # Avoid adding multiple handlers if imported multiple times or main app also configures
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# Create Blueprint
# The template_folder argument is not strictly necessary if send_from_directory is used with an absolute path
# or a path relative to the blueprint's root_path.
# os.path.dirname(os.path.abspath(__file__)) ensures the path is correct even if blueprint is registered from elsewhere.
ingestion_bp = Blueprint('ingestion_api', __name__, url_prefix=None) # No default prefix for now

# Configuration
UPLOAD_FOLDER = os.getenv("UPLOAD_FOLDER", "uploads")
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'md', 'csv', 'mp4', 'mp3', 'wav', 'm4a', 'vtt', 'srt'}
# MAX_CONTENT_LENGTH is usually set on the main app (e.g., _flask_app.config['MAX_CONTENT_LENGTH'])

# API Key Configuration
API_KEY = os.getenv("INGESTION_API_KEY")
if not API_KEY:
    logger.warning("INGESTION_API_KEY environment variable is not set. API will be unprotected for local development.")

# Ensure upload folder exists
if not os.path.exists(UPLOAD_FOLDER):
    try:
        os.makedirs(UPLOAD_FOLDER)
        logger.info(f"Created upload folder: {UPLOAD_FOLDER}")
    except OSError as e:
        logger.error(f"Failed to create upload folder {UPLOAD_FOLDER}: {e}", exc_info=True)
        # Depending on severity, might raise an error or exit

# Initialize Supabase client
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_KEY")
supabase: SupabaseClient | None = None

if supabase_url and supabase_key:
    try:
        # persist_session=False is good for server-side usage where sessions aren't user-bound
        supabase_options = ClientOptions(persist_session=False)
        supabase = create_client(supabase_url, supabase_key, options=supabase_options)
        logger.info("Supabase client initialized successfully for ingestion API blueprint using environment variables.")
    except Exception as e:
        logger.error(f"Failed to initialize Supabase client for ingestion API blueprint using environment variables: {e}", exc_info=True)
else:
    logger.error("SUPABASE_URL or SUPABASE_KEY environment variables not found or not set. Supabase client not initialized for ingestion API blueprint.")
    
def allowed_file(filename: str) -> bool:
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Authentication Decorator
def api_key_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if API_KEY: # Only enforce API key if it's configured
            provided_key = request.headers.get('X-API-Key')
            if not provided_key or provided_key != API_KEY:
                logger.warning(f"Unauthorized API access attempt. Provided key: {provided_key}")
                abort(401, description="Unauthorized: Invalid or missing API Key.")
        return f(*args, **kwargs)
    return decorated_function

# Error Handlers for the Blueprint
@ingestion_bp.errorhandler(BadRequest)
@ingestion_bp.errorhandler(400)
def handle_bad_request(e):
    description = getattr(e, 'description', str(e))
    logger.error(f"BadRequest (400): {description}", exc_info=True)
    return jsonify(error=description), 400

@ingestion_bp.errorhandler(Unauthorized)
@ingestion_bp.errorhandler(401)
def handle_unauthorized(e):
    description = getattr(e, 'description', str(e))
    logger.warning(f"Unauthorized (401): {description}") # No need for full exc_info for typical auth failures
    return jsonify(error=description), 401

@ingestion_bp.errorhandler(NotFound)
@ingestion_bp.errorhandler(404)
def handle_not_found(e):
    description = getattr(e, 'description', str(e))
    logger.warning(f"NotFound (404): {description}")
    return jsonify(error=description), 404

@ingestion_bp.errorhandler(UnsupportedMediaType)
@ingestion_bp.errorhandler(415)
def handle_unsupported_media_type(e):
    description = getattr(e, 'description', str(e))
    logger.error(f"UnsupportedMediaType (415): {description}", exc_info=True)
    return jsonify(error=description), 415

@ingestion_bp.errorhandler(Conflict)
@ingestion_bp.errorhandler(409)
def handle_conflict(e):
    description = getattr(e, 'description', str(e))
    logger.warning(f"Conflict (409): {description}", exc_info=True)
    return jsonify(error=description), 409

@ingestion_bp.errorhandler(InternalServerError)
@ingestion_bp.errorhandler(Exception) # Catch-all for other 500-level or unhandled exceptions
@ingestion_bp.errorhandler(500)
def handle_internal_server_error(e):
    # If it's a Werkzeug HTTPException, it has a description. Otherwise, it's a raw exception.
    description = getattr(e, 'description', "An unexpected internal error occurred.")
    if not isinstance(e, (BadRequest, Unauthorized, NotFound, UnsupportedMediaType, Conflict, InternalServerError)):
        # For truly unexpected exceptions, log more verbosely.
        logger.error(f"Unhandled Exception (500): {str(e)}", exc_info=True)
    else: # For Werkzeug's InternalServerError or re-raised ones
        logger.error(f"InternalServerError (500): {description}", exc_info=True)
    return jsonify(error=description), 500
    
@ingestion_bp.route("/api/v1/health", methods=["GET"])
def health_check():
    db_status = "not_initialized"
    if supabase:
        try:
            # A light query to check DB health. Example: get a single tag.
            # Adjust table/query as needed for your schema.
            # response = supabase.table("tags").select("id").limit(1).execute()
            # if response.data is not None: # Check if data attribute exists and is not None
            db_status = "ok" # Simplified check for now
            # else:
            # db_status = "query_failed_no_data"
        except Exception as e:
            logger.error(f"Supabase health check query failed: {e}", exc_info=True)
            db_status = "error"
            
    return jsonify({
        "status": "healthy", 
        "service": "ingestion_api",
        "timestamp": datetime.utcnow().isoformat(),
        "dependencies": {
            "supabase_connection": db_status
        }
    }), 200

@ingestion_bp.route("/api/v1/documents/ingest-file", methods=["POST"])
@api_key_required
def ingest_file_endpoint():
    if 'file' not in request.files:
        abort(400, description="No file part in the request.")
    file = request.files['file']
    if not file or not file.filename:
        abort(400, description="No file selected for uploading.")
    
    if allowed_file(file.filename):
        filename = secure_filename(file.filename)
        # Ensure UPLOAD_FOLDER is writable
        if not os.access(UPLOAD_FOLDER, os.W_OK):
            logger.error(f"Upload folder {UPLOAD_FOLDER} is not writable.")
            abort(500, description="Server configuration error: Upload folder not writable.")
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        
        try:
            file.save(filepath)
            logger.info(f"File '{filename}' saved temporarily to '{filepath}' for processing.")

            tags_str = request.form.get("tags", "")
            tags = [tag.strip() for tag in tags_str.split(',') if tag.strip()] if tags_str else []
            
            chunk_size_str = request.form.get("chunk_size")
            chunk_size = int(chunk_size_str) if chunk_size_str and chunk_size_str.isdigit() else None
            
            overlap_str = request.form.get("overlap")
            overlap = int(overlap_str) if overlap_str and overlap_str.isdigit() else None
            
            location_filter = request.form.get("location_filter")
            
            user_metadata_str = request.form.get("user_metadata")
            user_metadata = None
            if user_metadata_str:
                try:
                    user_metadata = json.loads(user_metadata_str)
                except json.JSONDecodeError:
                    abort(400, description="Invalid JSON format for user_metadata.")

            if not supabase:
                abort(500, description="Supabase client not initialized. Cannot process document.")

            document_id, chunks_count = process_document_from_file(
                filepath, filename, supabase, tags, chunk_size, overlap, location_filter, user_metadata
            )
            
            return jsonify({
                "message": "File ingested successfully.",
                "document_id": str(document_id), # Ensure UUID is stringified
                "filename": filename,
                "chunks_count": chunks_count,
                "tags_applied": tags
            }), 200

        except IngestionError as e:
            logger.error(f"IngestionError for file {filename}: {e}", exc_info=True)
            abort(500, description=f"Failed to ingest file: {e}")
        # Werkzeug exceptions (like BadRequest from abort) will be caught by their handlers
        except Exception as e: # Catch any other unexpected errors
            logger.error(f"Unexpected error ingesting file {filename}: {e}", exc_info=True)
            abort(500, description=f"An unexpected error occurred: {str(e)}")
        finally:
            # Clean up the uploaded file after processing, regardless of success or failure
            if os.path.exists(filepath):
                try:
                    os.remove(filepath)
                    logger.info(f"Cleaned up uploaded file: {filepath}")
                except OSError as e_remove:
                    logger.error(f"Error deleting uploaded file {filepath}: {e_remove.strerror}")
    else:
        abort(400, description=f"File type not allowed. Allowed extensions: {ALLOWED_EXTENSIONS}")

@ingestion_bp.route("/api/v1/documents/list", methods=["GET"])
@api_key_required
def list_documents_endpoint():
    """List all documents in the knowledge base"""
    if not supabase:
        abort(500, description="Supabase client not initialized.")

    try:
        # Query documents from the knowledge base
        response = supabase.table("knowledge_base").select(
            "document_id, source_identifier, source_type, metadata, created_at"
        ).order("created_at", desc=True).execute()

        documents = []
        seen_docs = set()

        for row in response.data:
            doc_id = row.get("document_id")
            if doc_id in seen_docs:
                continue
            seen_docs.add(doc_id)

            metadata = row.get("metadata", {})

            # Count chunks for this document
            chunks_response = supabase.table("knowledge_base").select(
                "id", only_count=True
            ).eq("document_id", doc_id).execute()

            documents.append({
                "document_id": doc_id,
                "filename": metadata.get("filename", row.get("source_identifier", "Unknown")),
                "document_type": metadata.get("document_type", "general_knowledge"),
                "priority": metadata.get("priority", "medium"),
                "description": metadata.get("description", ""),
                "tags": ", ".join(metadata.get("document_tags", [])),
                "chunks_count": chunks_response.count,
                "created_at": row.get("created_at"),
                "file_size": metadata.get("file_size_bytes", 0),
                "source_type": row.get("source_type")
            })

        return jsonify(documents), 200

    except Exception as e:
        logger.error(f"Error listing documents: {e}", exc_info=True)
        abort(500, description=f"Failed to list documents: {str(e)}")

@ingestion_bp.route("/api/v1/documents/<document_id>/chunks", methods=["GET"])
@api_key_required
def get_document_chunks_endpoint(document_id: str):
    """Get chunks for a specific document"""
    if not supabase:
        abort(500, description="Supabase client not initialized.")

    try:
        response = supabase.table("knowledge_base").select(
            "id, content, metadata, chunk_index"
        ).eq("document_id", document_id).order("chunk_index").execute()

        chunks = []
        for row in response.data:
            content = row.get("content", "")
            chunks.append({
                "id": row.get("id"),
                "chunk_index": row.get("chunk_index", 0),
                "content_preview": content[:200] + "..." if len(content) > 200 else content,
                "chunk_size": len(content),
                "full_content": content
            })

        return jsonify(chunks), 200

    except Exception as e:
        logger.error(f"Error getting document chunks: {e}", exc_info=True)
        abort(500, description=f"Failed to get document chunks: {str(e)}")

@ingestion_bp.route("/api/v1/documents/<document_id>", methods=["DELETE"])
@api_key_required
def delete_document_endpoint(document_id: str):
    """Delete a document and all its chunks"""
    if not supabase:
        abort(500, description="Supabase client not initialized.")

    try:
        # Delete all chunks for this document
        response = supabase.table("knowledge_base").delete().eq("document_id", document_id).execute()

        return jsonify({
            "message": f"Document {document_id} and all its chunks deleted successfully",
            "deleted_chunks": len(response.data) if response.data else 0
        }), 200

    except Exception as e:
        logger.error(f"Error deleting document: {e}", exc_info=True)
        abort(500, description=f"Failed to delete document: {str(e)}")

@ingestion_bp.route("/api/v1/documents/search", methods=["POST"])
@api_key_required
def search_documents_endpoint():
    """Search documents by content or metadata"""
    if not request.is_json:
        abort(415, description="Request must be JSON.")

    data = request.get_json()
    if not data or "query" not in data:
        abort(400, description="Missing 'query' in JSON payload.")

    query_text = data["query"]
    limit = data.get("limit", 10)
    tags_filter = data.get("tags", [])
    document_type_filter = data.get("document_type")

    if not supabase:
        abort(500, description="Supabase client not initialized.")

    try:
        # Build query
        query = supabase.table("knowledge_base").select(
            "document_id, source_identifier, content, metadata, similarity"
        )

        # Add filters
        if tags_filter:
            for tag in tags_filter:
                query = query.contains("metadata->document_tags", [tag])

        if document_type_filter:
            query = query.eq("metadata->document_type", document_type_filter)

        # Execute semantic search (if vector search is available)
        # For now, use text search
        query = query.textSearch("content", query_text)

        response = query.limit(limit).execute()

        return jsonify(response.data), 200

    except Exception as e:
        logger.error(f"Error searching documents: {e}", exc_info=True)
        abort(500, description=f"Failed to search documents: {str(e)}")

@ingestion_bp.route("/api/v1/documents/ingest-url", methods=["POST"])
@api_key_required
def ingest_url_endpoint():
    if not request.is_json:
        abort(415, description="Request must be JSON.")
    
    data = request.get_json()
    if not data or "url" not in data:
        abort(400, description="Missing 'url' in JSON payload.")

    url_to_ingest = data["url"]
    tags_input = data.get("tags", [])
    tags: list[str] = []
    if isinstance(tags_input, str):
        tags = [tag.strip() for tag in tags_input.split(',') if tag.strip()]
    elif isinstance(tags_input, list):
        tags = [str(tag).strip() for tag in tags_input if str(tag).strip()]

    chunk_size_str = data.get("chunk_size")
    chunk_size = int(chunk_size_str) if chunk_size_str and str(chunk_size_str).isdigit() else None
            
    overlap_str = data.get("overlap")
    overlap = int(overlap_str) if overlap_str and str(overlap_str).isdigit() else None

    location_filter = data.get("location_filter")
    user_metadata_str = data.get("user_metadata")
    user_metadata = None
    if user_metadata_str:
        if isinstance(user_metadata_str, str):
            try:
                user_metadata = json.loads(user_metadata_str)
            except json.JSONDecodeError:
                abort(400, description="Invalid JSON format for user_metadata string.")
        elif isinstance(user_metadata_str, dict): # Already a dict
            user_metadata = user_metadata_str
        else:
            abort(400, description="user_metadata must be a JSON string or a JSON object.")

    if not supabase:
        abort(500, description="Supabase client not initialized. Cannot process document from URL.")

    try:
        document_id, chunks_count = process_document_from_url(
            url_to_ingest, supabase, tags, chunk_size, overlap, location_filter, user_metadata
        )
        return jsonify({
            "message": "URL ingested successfully.",
            "document_id": str(document_id), # Ensure UUID is stringified
            "url": url_to_ingest,
            "chunks_count": chunks_count,
            "tags_applied": tags
        }), 200
    except IngestionError as e:
        logger.error(f"IngestionError for URL {url_to_ingest}: {e}", exc_info=True)
        abort(500, description=f"Failed to ingest URL: {e}")
    except Exception as e:
        logger.error(f"Unexpected error ingesting URL {url_to_ingest}: {e}", exc_info=True)
        abort(500, description=f"An unexpected error occurred: {str(e)}")

# --- Tag Management Endpoints ---

@ingestion_bp.route("/api/v1/tags", methods=["POST"])
@api_key_required
def create_tag_endpoint():
    if not request.is_json:
        abort(415, description="Request must be JSON.")
    
    data = request.get_json()
    if not data or "name" not in data:
        abort(400, description="Missing 'name' in JSON payload.")

    tag_name = str(data["name"]).strip()
    if not tag_name:
        abort(400, description="'name' cannot be empty.")

    if not supabase:
        abort(500, description="Supabase client not initialized.")

    try:
        # Upsert ensures that if the tag name already exists, it doesn't create a duplicate.
        response = supabase.table("tags").upsert({"name": tag_name}, on_conflict="name").execute()
        
        if response.data and len(response.data) > 0:
            return jsonify(response.data[0]), 201 # 201 Created (or 200 OK if it already existed via upsert)
        else:
            # This case might happen if upsert doesn't return data but operation was successful.
            # Let's try to select to confirm.
            logger.warning(f"Tag upsert for '{tag_name}' returned no data. Verifying existence.")
            select_response = supabase.table("tags").select("*").eq("name", tag_name).maybe_single().execute()
            if select_response.data:
                 return jsonify(select_response.data), 200 # Existed or was created
            abort(500, description=f"Failed to create or retrieve tag '{tag_name}' after upsert.")

    except Exception as e: # Catch Supabase/Postgrest errors more specifically if possible
        logger.error(f"Error creating tag '{tag_name}': {e}", exc_info=True)
        # Example: if e is PostgrestAPIError and e.code == '23505' (unique_violation) for 'tags_name_key'
        if "unique constraint" in str(e).lower() and ("tags_name_key" in str(e).lower() or "tags_name_idx" in str(e).lower()): # Adjusted for common index names
             abort(409, description=f"Tag with name '{tag_name}' already exists.")
        abort(500, description=f"Could not create tag: {str(e)}")

@ingestion_bp.route("/api/v1/tags", methods=["GET"])
@api_key_required
def list_tags_endpoint():
    if not supabase:
        abort(500, description="Supabase client not initialized.")

    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        if page < 1: page = 1
        if per_page < 1: per_page = 1
        if per_page > 100: per_page = 100 # Max limit

        offset = (page - 1) * per_page

        query = supabase.table("tags").select("*", count="exact").order("name").range(offset, offset + per_page - 1)
        response = query.execute()
        
        total_tags = response.count if response.count is not None else 0
        
        return jsonify({
            "tags": response.data,
            "total_tags": total_tags,
            "page": page,
            "per_page": per_page,
            "total_pages": (total_tags + per_page - 1) // per_page if per_page > 0 and total_tags > 0 else 0
        }), 200
        
    except Exception as e:
        logger.error(f"Error listing tags: {e}", exc_info=True)
        abort(500, description=f"Could not retrieve tags: {str(e)}")

@ingestion_bp.route("/api/v1/tags/<path:identifier>", methods=["GET"])
@api_key_required
def get_tag_endpoint(identifier: str):
    if not supabase:
        abort(500, description="Supabase client not initialized.")

    try:
        is_id = identifier.isdigit()
        
        if is_id:
            tag_id = int(identifier)
            query = supabase.table("tags").select("*").eq("id", tag_id).single()
        else:
            tag_name = identifier # Name is a string
            query = supabase.table("tags").select("*").eq("name", tag_name).single()
        
        response = query.execute()
        
        if response.data:
            return jsonify(response.data), 200
        else:
            abort(404, description=f"Tag with {'ID' if is_id else 'name'} '{identifier}' not found.")
            
    except Exception as e:
        logger.error(f"Error retrieving tag '{identifier}': {e}", exc_info=True)
        abort(500, description=f"Could not retrieve tag: {str(e)}")

# --- Document-Tag Association Endpoints ---

def _get_tag_id_from_identifier(identifier: str) -> int | None:
    """Helper to resolve a tag identifier (ID or name) to a tag ID."""
    if not supabase:
        # This is an internal function, so raising InternalServerError is appropriate
        # if called when supabase is not available.
        logger.error("Supabase client not initialized during tag ID lookup.")
        raise InternalServerError("Supabase client not initialized.") 
    
    tag_id_to_use: int | None = None
    if identifier.isdigit():
        try:
            tag_id_to_use = int(identifier)
            # Verify this tag_id exists
            tag_check = supabase.table("tags").select("id", count="exact").eq("id", tag_id_to_use).execute()
            if tag_check.count == 0: # type: ignore
                return None # Tag ID does not exist
        except ValueError: # Should not happen if isdigit() is true, but defensive.
            return None
    else:
        # Identifier is a name, find its ID
        tag_record = supabase.table("tags").select("id").eq("name", identifier).maybe_single().execute()
        if tag_record.data:
            tag_id_to_use = tag_record.data["id"]
        else:
            return None # Tag name does not exist
    return tag_id_to_use

@ingestion_bp.route("/api/v1/documents/<uuid:document_id>/tags", methods=["POST"])
@api_key_required
def associate_tag_with_document_endpoint(document_id): # document_id is already a UUID object
    if not request.is_json:
        abort(415, description="Request must be JSON.")
    
    data = request.get_json()
    if not data or ('tag_id' not in data and 'tag_name' not in data):
        abort(400, description="Missing 'tag_id' or 'tag_name' in JSON payload.")

    if not supabase:
        abort(500, description="Supabase client not initialized.")

    # Verify document exists
    doc_check = supabase.table("documents").select("id", count="exact").eq("id", str(document_id)).execute()
    if doc_check.count == 0: # type: ignore
        abort(404, description=f"Document with ID '{document_id}' not found.")

    tag_id_to_associate: int | None = None
    if "tag_id" in data:
        if not isinstance(data["tag_id"], int):
            abort(400, description="'tag_id' must be an integer.")
        tag_id_to_associate = data["tag_id"]
        # Verify tag_id exists
        tag_check = supabase.table("tags").select("id", count="exact").eq("id", tag_id_to_associate).execute()
        if tag_check.count == 0: # type: ignore
            abort(404, description=f"Tag with ID '{tag_id_to_associate}' not found.")
    elif "tag_name" in data:
        tag_name = str(data["tag_name"]).strip()
        if not tag_name:
            abort(400, description="'tag_name' cannot be empty.")
        
        tag_id_to_associate = _get_tag_id_from_identifier(tag_name)
        if tag_id_to_associate is None:
            abort(404, description=f"Tag with name '{tag_name}' not found. Create it first using POST /api/v1/tags.")
            
    if tag_id_to_associate is None: # Should be caught by earlier checks
        abort(400, description="Could not determine tag ID for association.")

    try:
        association_data = {"document_id": str(document_id), "tag_id": tag_id_to_associate}
        # Upsert to handle cases where the association might already exist.
        response = supabase.table("document_tags").upsert(association_data, on_conflict="document_id, tag_id").execute()
        
        # Check if data was returned (it might not on simple upsert if no change)
        # For simplicity, if no error, assume success.
        return jsonify({"message": "Tag associated with document successfully.", "association": association_data}), 200
        
    except Exception as e:
        logger.error(f"Error associating tag ID {tag_id_to_associate} with document {document_id}: {e}", exc_info=True)
        if "violates foreign key constraint" in str(e).lower(): # More specific error check
            abort(404, description="Document ID or Tag ID does not exist for association.")
        abort(500, description=f"Could not associate tag: {str(e)}")

@ingestion_bp.route("/api/v1/documents/<uuid:document_id>/tags", methods=["GET"])
@api_key_required
def list_document_tags_endpoint(document_id): # document_id is UUID object
    if not supabase:
        abort(500, description="Supabase client not initialized.")

    # Verify document exists
    doc_check = supabase.table("documents").select("id", count="exact").eq("id", str(document_id)).execute()
    if doc_check.count == 0: # type: ignore
        abort(404, description=f"Document with ID '{document_id}' not found.")

    try:
        # Join document_tags with tags table to get tag names and other details
        response = supabase.table("document_tags").select("tags(*)").eq("document_id", str(document_id)).execute()
        
        tags_data = [item['tags'] for item in response.data if item.get('tags') is not None] # Ensure 'tags' key exists
        return jsonify({"document_id": str(document_id), "tags": tags_data}), 200
        
    except Exception as e:
        logger.error(f"Error listing tags for document {document_id}: {e}", exc_info=True)
        abort(500, description=f"Could not retrieve tags for document: {str(e)}")

@ingestion_bp.route("/api/v1/documents/<uuid:document_id>/tags/<path:tag_identifier>", methods=["DELETE"])
@api_key_required
def disassociate_tag_from_document_endpoint(document_id, tag_identifier: str): # document_id is UUID
    if not supabase:
        abort(500, description="Supabase client not initialized.")

    doc_check = supabase.table("documents").select("id", count="exact").eq("id", str(document_id)).execute()
    if doc_check.count == 0: # type: ignore
        abort(404, description=f"Document with ID '{document_id}' not found.")

    tag_id_to_remove = _get_tag_id_from_identifier(tag_identifier)
    if tag_id_to_remove is None:
        abort(404, description=f"Tag with identifier '{tag_identifier}' not found.")

    try:
        response = supabase.table("document_tags").delete().match({"document_id": str(document_id), "tag_id": tag_id_to_remove}).execute()
        
        # response.data for delete usually contains the deleted records.
        # If response.data is empty or count is 0, it means no record matched.
        if response.data and len(response.data) > 0: # Or check response.count if available and reliable for delete
            return jsonify({"message": "Tag disassociated from document successfully."}), 200 # Or 204 No Content
        else:
            # This means the tag wasn't associated with the document.
            return jsonify({"message": "Tag was not associated with this document or already removed."}), 200 # Or 404 for the association
            
    except Exception as e:
        logger.error(f"Error disassociating tag ID {tag_id_to_remove} from document {document_id}: {e}", exc_info=True)
        abort(500, description=f"Could not disassociate tag: {str(e)}")

@ingestion_bp.route("/api/v1/tags/<path:identifier>", methods=["PUT"])
@api_key_required
def update_tag_endpoint(identifier: str):
    if not request.is_json:
        abort(415, description="Request must be JSON.")
    
    data = request.get_json()
    if not data or "new_name" not in data:
        abort(400, description="Missing 'new_name' in JSON payload.")

    new_name = str(data["new_name"]).strip()
    if not new_name:
        abort(400, description="'new_name' cannot be empty.")

    if not supabase:
        abort(500, description="Supabase client not initialized.")

    tag_id_to_update = _get_tag_id_from_identifier(identifier)
    if tag_id_to_update is None:
        abort(404, description=f"Tag with identifier '{identifier}' not found for update.")

    try:
        # Check if new_name already exists for another tag
        existing_tag_with_new_name = supabase.table("tags").select("id", count="exact").eq("name", new_name).not_("id", "eq", tag_id_to_update).execute()
        if existing_tag_with_new_name.count > 0: # type: ignore
            abort(409, description=f"A tag with name '{new_name}' already exists.")

        response = supabase.table("tags").update({"name": new_name}).eq("id", tag_id_to_update).execute()
        
        if response.data and len(response.data) > 0:
            return jsonify(response.data[0]), 200
        else:
            # This might happen if the ID was valid but update failed without error, or no record matched.
            # _get_tag_id_from_identifier should ensure it exists.
            # If no change was made because new_name is same as old_name, it might also return no data.
            # Check if the tag now has the new name.
            updated_tag_check = supabase.table("tags").select("*").eq("id", tag_id_to_update).eq("name", new_name).maybe_single().execute()
            if updated_tag_check.data:
                return jsonify(updated_tag_check.data), 200
            abort(404, description=f"Tag with identifier '{identifier}' found but could not be updated (or no change made).")
            
    except Exception as e:
        logger.error(f"Error updating tag '{identifier}' to '{new_name}': {e}", exc_info=True)
        if "unique constraint" in str(e).lower() and ("tags_name_key" in str(e).lower() or "tags_name_idx" in str(e).lower()): # Should be caught by pre-check
             abort(409, description=f"Tag name '{new_name}' conflicts with an existing tag.")
        abort(500, description=f"Could not update tag: {str(e)}")

@ingestion_bp.route("/api/v1/tags/<path:identifier>", methods=["DELETE"])
@api_key_required
def delete_tag_endpoint(identifier: str):
    if not supabase:
        abort(500, description="Supabase client not initialized.")

    tag_id_to_delete = _get_tag_id_from_identifier(identifier)
    if tag_id_to_delete is None:
        abort(404, description=f"Tag with identifier '{identifier}' not found for deletion.")

    try:
        # Associations in 'document_tags' should be deleted automatically due to ON DELETE CASCADE.
        response = supabase.table("tags").delete().eq("id", tag_id_to_delete).execute()
        
        if response.data and len(response.data) > 0: # Or check response.count
            return jsonify({"message": f"Tag '{identifier}' (ID: {tag_id_to_delete}) deleted successfully."}), 200 # Or 204 No Content
        else:
            # This implies the tag was found by _get_tag_id_from_identifier but delete returned no data.
            # Could mean it was deleted between check and delete (race condition) or issue with client/DB.
            # Or it simply means the delete operation affected 0 rows but didn't error.
            # Check if it still exists.
            check_again = supabase.table("tags").select("id", count="exact").eq("id", tag_id_to_delete).execute()
            if check_again.count == 0: # type: ignore
                 return jsonify({"message": f"Tag '{identifier}' (ID: {tag_id_to_delete}) deleted successfully (or was already gone)."}), 200 # Or 204
            abort(404, description=f"Tag with identifier '{identifier}' found but could not be confirmed as deleted.")
            
    except Exception as e:
        logger.error(f"Error deleting tag '{identifier}': {e}", exc_info=True)
        abort(500, description=f"Could not delete tag: {str(e)}")

@ingestion_bp.route("/", methods=["GET"])
def serve_frontend():
    """
    Serves the frontend.html file.
    Assumes frontend.html is in the same directory as this ingestion_api.py file.
    """
    # os.path.abspath ensures that the path is absolute before passing to send_from_directory
    # os.path.dirname(__file__) gets the directory of the current script (ingestion_api.py)
    blueprint_dir = os.path.dirname(os.path.abspath(__file__))
    return send_from_directory(blueprint_dir, "frontend.html")

# Create Flask application instance
from flask import Flask
from flask_cors import CORS

def create_app():
    """Create and configure the Flask application"""
    app = Flask(__name__)
    
    # Configure CORS
    CORS(app, origins=["http://localhost:3000", "http://localhost:5173", "http://localhost:8080"])
    
    # Configure Flask app settings
    app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size
    app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
    
    # Register the blueprint
    app.register_blueprint(ingestion_bp)
    
    return app

# Create the app instance for ASGI server
app = create_app()

if __name__ == "__main__":
    # For development - run with Flask's built-in server
    app.run(host="0.0.0.0", port=int(os.getenv("PORT", 5002)), debug=os.getenv("DEBUG_MODE", "false").lower() == "true")
