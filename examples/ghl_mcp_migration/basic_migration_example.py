"""
Example: Migrating from basic GHL client to comprehensive MCP client
"""

# OLD WAY (basic ghl_client.py)
from agents.ghl_client import GHLClient

def old_create_contact_workflow(contact_data):
    client = GHLClient()
    
    # Basic contact creation
    result = client.send_sms(contact_data["id"], "Welcome message")
    return result

# NEW WAY (comprehensive MCP client with 269+ tools)
from agents.ghl_mcp_client import GHLMC<PERSON>lient

def new_create_contact_workflow(contact_data, property_data):
    client = GHLMCPClient()
    
    # Complete workflow with multiple tools
    result = client.create_lead_workflow(contact_data, property_data)
    
    # Additional capabilities now available:
    # - Advanced contact management with custom fields
    # - Automated tagging and organization
    # - Task creation and assignment
    # - Workflow automation
    # - Multi-channel messaging (SMS + Email)
    # - Opportunity pipeline management
    # - Calendar integration
    # - And 260+ more tools!
    
    return result

# Example usage
if __name__ == "__main__":
    contact_data = {
        "first_name": "<PERSON>",
        "last_name": "Seller",
        "email": "<EMAIL>",
        "phone": "+1555123456",
        "source": "website",
        "tier": "1"
    }
    
    property_data = {
        "address": "123 Main St",
        "property_type": "single-family",
        "estimated_value": 250000.0,
        "pipeline_id": "your_pipeline_id"
    }
    
    # Use the new comprehensive workflow
    result = new_create_contact_workflow(contact_data, property_data)
    print("Workflow result:", result)
