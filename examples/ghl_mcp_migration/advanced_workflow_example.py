"""
Example: Advanced real estate workflow using MCP tools
"""

from agents.ghl_mcp_client import GHLMCPClient
from datetime import datetime, timed<PERSON><PERSON>

def comprehensive_lead_processing(lead_data):
    """
    Process a real estate lead using the full power of 269+ MCP tools.
    This replaces multiple separate functions with one comprehensive workflow.
    """
    client = GHLMCPClient()
    results = {}
    
    # 1. Create contact with advanced data
    contact_result = client.create_contact(
        first_name=lead_data["first_name"],
        last_name=lead_data["last_name"],
        email=lead_data["email"],
        phone=lead_data["phone"],
        address=lead_data["property_address"],
        custom_fields={
            "property_address": lead_data["property_address"],
            "lead_source": lead_data["source"],
            "tier": lead_data["tier"],
            "property_type": lead_data.get("property_type", ""),
            "estimated_value": lead_data.get("estimated_value", 0)
        }
    )
    results["contact_creation"] = contact_result
    
    if "error" not in contact_result:
        contact_id = contact_result["contact"]["id"]
        
        # 2. Advanced tagging based on lead characteristics
        tags = ["real-estate-lead", f"tier-{lead_data['tier']}"]
        if lead_data.get("property_type"):
            tags.append(f"property-{lead_data['property_type']}")
        if lead_data.get("source"):
            tags.append(f"source-{lead_data['source']}")
        
        results["tagging"] = client.add_contact_tags(contact_id, tags)
        
        # 3. Create opportunity in sales pipeline
        results["opportunity"] = client.create_opportunity(
            contact_id=contact_id,
            pipeline_id=lead_data.get("pipeline_id", "default_pipeline"),
            title=f"Property: {lead_data['property_address']}",
            value=lead_data.get("estimated_value", 0)
        )
        
        # 4. Send welcome message sequence
        welcome_sms = f"Hi {lead_data['first_name']}! Thanks for your interest in selling {lead_data['property_address']}. I'm analyzing your property value and will have more information soon."
        results["welcome_sms"] = client.send_sms(contact_id, welcome_sms)
        
        # 5. Create follow-up tasks
        results["analysis_task"] = client.create_contact_task(
            contact_id=contact_id,
            title="Property Analysis",
            description=f"Complete market analysis for {lead_data['property_address']}",
            due_date=(datetime.now() + timedelta(hours=2)).strftime("%Y-%m-%d")
        )
        
        results["followup_task"] = client.create_contact_task(
            contact_id=contact_id,
            title="Follow-up Call",
            description="Call to discuss property details and potential offer",
            due_date=(datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
        )
        
        # 6. Add to nurture workflow if tier 2 or 3
        if lead_data.get("tier") in ["2", "3"]:
            results["workflow"] = client.add_contact_to_workflow(
                contact_id=contact_id,
                workflow_id="nurture_sequence_workflow"
            )
    
    return results

def send_offer_with_full_automation(contact_id, offer_data):
    """
    Send an offer using comprehensive automation tools.
    """
    client = GHLMCPClient()
    
    # Send offer notification via multiple channels
    offer_result = client.send_offer_notification(
        contact_id=contact_id,
        offer_amount=offer_data["amount"],
        property_address=offer_data["property_address"],
        offer_details=offer_data.get("details", {})
    )
    
    # Create opportunity for offer tracking
    opportunity_result = client.create_opportunity(
        contact_id=contact_id,
        pipeline_id=offer_data.get("pipeline_id", "offers_pipeline"),
        title=f"Offer: ${offer_data['amount']:,.2f} - {offer_data['property_address']}",
        value=offer_data["amount"]
    )
    
    # Schedule follow-up appointment
    if offer_data.get("schedule_followup"):
        appointment_result = client.create_appointment(
            calendar_id=offer_data.get("calendar_id", "default_calendar"),
            contact_id=contact_id,
            title=f"Offer Discussion - {offer_data['property_address']}",
            start_time=offer_data.get("followup_time"),
            end_time=offer_data.get("followup_end_time"),
            description=f"Discuss offer of ${offer_data['amount']:,.2f} for {offer_data['property_address']}"
        )
    
    return {
        "offer_notification": offer_result,
        "opportunity": opportunity_result,
        "appointment": appointment_result if offer_data.get("schedule_followup") else None
    }

# Example usage
if __name__ == "__main__":
    # Process a comprehensive lead
    lead_data = {
        "first_name": "Sarah",
        "last_name": "Johnson", 
        "email": "<EMAIL>",
        "phone": "+1555987654",
        "property_address": "456 Oak Street, City, ST 12345",
        "source": "facebook_ad",
        "tier": "1",
        "property_type": "single-family",
        "estimated_value": 275000,
        "pipeline_id": "main_pipeline"
    }
    
    result = comprehensive_lead_processing(lead_data)
    print("Lead processing result:", result)
