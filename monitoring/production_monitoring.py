"""
Production Monitoring System for AI-OS
Monitors system health, performance, and alerts on issues
"""

import logging
import time
import requests
import psutil
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import smtplib
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
import os
from supabase import create_client, Client

logger = logging.getLogger(__name__)

@dataclass
class HealthCheck:
    """Health check result"""
    service: str
    status: str  # 'healthy', 'warning', 'critical'
    response_time: float
    message: str
    timestamp: datetime

@dataclass
class Alert:
    """Alert configuration"""
    name: str
    condition: str
    threshold: float
    severity: str  # 'info', 'warning', 'critical'
    enabled: bool = True

class ProductionMonitor:
    """Production monitoring system"""
    
    def __init__(self):
        self.supabase = None
        self.alerts = self._load_alert_config()
        self.last_alert_times = {}
        self.alert_cooldown = 300  # 5 minutes
        
        # Initialize Supabase if available
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY")
        if supabase_url and supabase_key:
            try:
                self.supabase = create_client(supabase_url, supabase_key)
            except Exception as e:
                logger.error(f"Failed to initialize Supabase: {e}")
    
    def _load_alert_config(self) -> List[Alert]:
        """Load alert configuration"""
        return [
            Alert("api_response_time", "response_time > threshold", 5.0, "warning"),
            Alert("api_availability", "status != 'healthy'", 0, "critical"),
            Alert("retool_availability", "status != 'healthy'", 0, "critical"),
            Alert("disk_usage", "disk_usage > threshold", 85.0, "warning"),
            Alert("memory_usage", "memory_usage > threshold", 90.0, "warning"),
            Alert("cpu_usage", "cpu_usage > threshold", 80.0, "warning"),
            Alert("database_connections", "active_connections > threshold", 80, "warning"),
            Alert("error_rate", "error_rate > threshold", 5.0, "warning"),
        ]
    
    def check_api_health(self, base_url: str = "http://localhost:5002") -> HealthCheck:
        """Check AI-OS API health"""
        try:
            start_time = time.time()
            response = requests.get(f"{base_url}/api/v1/health", timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                return HealthCheck(
                    service="ai_os_api",
                    status="healthy",
                    response_time=response_time,
                    message="API responding normally",
                    timestamp=datetime.now()
                )
            else:
                return HealthCheck(
                    service="ai_os_api",
                    status="warning",
                    response_time=response_time,
                    message=f"API returned status {response.status_code}",
                    timestamp=datetime.now()
                )
        except Exception as e:
            return HealthCheck(
                service="ai_os_api",
                status="critical",
                response_time=0,
                message=f"API unreachable: {str(e)}",
                timestamp=datetime.now()
            )
    
    def check_retool_health(self, base_url: str = "http://localhost:3000") -> HealthCheck:
        """Check Retool health"""
        try:
            start_time = time.time()
            response = requests.get(f"{base_url}/api/checkHealth", timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                return HealthCheck(
                    service="retool",
                    status="healthy",
                    response_time=response_time,
                    message="Retool responding normally",
                    timestamp=datetime.now()
                )
            else:
                return HealthCheck(
                    service="retool",
                    status="warning",
                    response_time=response_time,
                    message=f"Retool returned status {response.status_code}",
                    timestamp=datetime.now()
                )
        except Exception as e:
            return HealthCheck(
                service="retool",
                status="critical",
                response_time=0,
                message=f"Retool unreachable: {str(e)}",
                timestamp=datetime.now()
            )
    
    def check_system_resources(self) -> Dict[str, HealthCheck]:
        """Check system resource usage"""
        checks = {}
        
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        checks["cpu"] = HealthCheck(
            service="system_cpu",
            status="healthy" if cpu_percent < 80 else "warning" if cpu_percent < 95 else "critical",
            response_time=0,
            message=f"CPU usage: {cpu_percent:.1f}%",
            timestamp=datetime.now()
        )
        
        # Memory usage
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        checks["memory"] = HealthCheck(
            service="system_memory",
            status="healthy" if memory_percent < 85 else "warning" if memory_percent < 95 else "critical",
            response_time=0,
            message=f"Memory usage: {memory_percent:.1f}%",
            timestamp=datetime.now()
        )
        
        # Disk usage
        disk = psutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100
        checks["disk"] = HealthCheck(
            service="system_disk",
            status="healthy" if disk_percent < 80 else "warning" if disk_percent < 95 else "critical",
            response_time=0,
            message=f"Disk usage: {disk_percent:.1f}%",
            timestamp=datetime.now()
        )
        
        return checks
    
    def check_database_health(self) -> HealthCheck:
        """Check database connectivity and performance"""
        if not self.supabase:
            return HealthCheck(
                service="database",
                status="warning",
                response_time=0,
                message="Database client not configured",
                timestamp=datetime.now()
            )
        
        try:
            start_time = time.time()
            # Simple query to test connectivity
            response = self.supabase.table("leads").select("id").limit(1).execute()
            response_time = time.time() - start_time
            
            return HealthCheck(
                service="database",
                status="healthy",
                response_time=response_time,
                message="Database responding normally",
                timestamp=datetime.now()
            )
        except Exception as e:
            return HealthCheck(
                service="database",
                status="critical",
                response_time=0,
                message=f"Database error: {str(e)}",
                timestamp=datetime.now()
            )
    
    def check_recent_errors(self) -> HealthCheck:
        """Check for recent errors in the system"""
        if not self.supabase:
            return HealthCheck(
                service="error_monitoring",
                status="warning",
                response_time=0,
                message="Error monitoring not configured",
                timestamp=datetime.now()
            )
        
        try:
            # Check for errors in the last hour
            one_hour_ago = (datetime.now() - timedelta(hours=1)).isoformat()
            response = self.supabase.table("agent_errors").select(
                "id", only_count=True
            ).gte("created_at", one_hour_ago).execute()
            
            error_count = response.count if response.count else 0
            
            if error_count == 0:
                status = "healthy"
                message = "No recent errors"
            elif error_count < 5:
                status = "warning"
                message = f"{error_count} errors in the last hour"
            else:
                status = "critical"
                message = f"{error_count} errors in the last hour - investigate immediately"
            
            return HealthCheck(
                service="error_monitoring",
                status=status,
                response_time=0,
                message=message,
                timestamp=datetime.now()
            )
        except Exception as e:
            return HealthCheck(
                service="error_monitoring",
                status="warning",
                response_time=0,
                message=f"Could not check errors: {str(e)}",
                timestamp=datetime.now()
            )
    
    def run_all_checks(self, base_url: str = None) -> Dict[str, HealthCheck]:
        """Run all health checks"""
        if not base_url:
            base_url = os.getenv("WEBHOOK_URL", "http://localhost")
        
        checks = {}
        
        # Service checks
        checks["api"] = self.check_api_health(f"{base_url}:5002" if "localhost" in base_url else base_url)
        checks["retool"] = self.check_retool_health(f"{base_url}:3000" if "localhost" in base_url else f"{base_url}/retool")
        checks["database"] = self.check_database_health()
        checks["errors"] = self.check_recent_errors()
        
        # System resource checks
        system_checks = self.check_system_resources()
        checks.update(system_checks)
        
        return checks
    
    def send_alert(self, check: HealthCheck, alert: Alert):
        """Send alert notification"""
        # Check cooldown
        alert_key = f"{check.service}_{alert.name}"
        now = datetime.now()
        
        if alert_key in self.last_alert_times:
            time_since_last = (now - self.last_alert_times[alert_key]).total_seconds()
            if time_since_last < self.alert_cooldown:
                return  # Skip due to cooldown
        
        self.last_alert_times[alert_key] = now
        
        # Log alert
        logger.warning(f"ALERT: {alert.name} - {check.service}: {check.message}")
        
        # Send email if configured
        self._send_email_alert(check, alert)
        
        # Log to database if available
        self._log_alert_to_database(check, alert)
    
    def _send_email_alert(self, check: HealthCheck, alert: Alert):
        """Send email alert"""
        smtp_server = os.getenv("SMTP_SERVER")
        smtp_port = int(os.getenv("SMTP_PORT", "587"))
        smtp_user = os.getenv("SMTP_USER")
        smtp_password = os.getenv("SMTP_PASSWORD")
        alert_email = os.getenv("ALERT_EMAIL")
        
        if not all([smtp_server, smtp_user, smtp_password, alert_email]):
            logger.warning("Email alerting not configured")
            return
        
        try:
            msg = MimeMultipart()
            msg['From'] = smtp_user
            msg['To'] = alert_email
            msg['Subject'] = f"AI-OS Alert: {alert.severity.upper()} - {check.service}"
            
            body = f"""
AI-OS Production Alert

Service: {check.service}
Status: {check.status}
Severity: {alert.severity}
Message: {check.message}
Time: {check.timestamp}
Response Time: {check.response_time:.2f}s

Please investigate immediately.
"""
            
            msg.attach(MimeText(body, 'plain'))
            
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(smtp_user, smtp_password)
            server.send_message(msg)
            server.quit()
            
            logger.info(f"Alert email sent for {check.service}")
            
        except Exception as e:
            logger.error(f"Failed to send alert email: {e}")
    
    def _log_alert_to_database(self, check: HealthCheck, alert: Alert):
        """Log alert to database"""
        if not self.supabase:
            return
        
        try:
            self.supabase.table("system_alerts").insert({
                "service": check.service,
                "alert_name": alert.name,
                "severity": alert.severity,
                "status": check.status,
                "message": check.message,
                "response_time": check.response_time,
                "created_at": check.timestamp.isoformat()
            }).execute()
        except Exception as e:
            logger.error(f"Failed to log alert to database: {e}")
    
    def generate_health_report(self, checks: Dict[str, HealthCheck]) -> Dict[str, Any]:
        """Generate comprehensive health report"""
        healthy_count = sum(1 for check in checks.values() if check.status == "healthy")
        warning_count = sum(1 for check in checks.values() if check.status == "warning")
        critical_count = sum(1 for check in checks.values() if check.status == "critical")
        
        overall_status = "healthy"
        if critical_count > 0:
            overall_status = "critical"
        elif warning_count > 0:
            overall_status = "warning"
        
        return {
            "timestamp": datetime.now().isoformat(),
            "overall_status": overall_status,
            "summary": {
                "total_checks": len(checks),
                "healthy": healthy_count,
                "warning": warning_count,
                "critical": critical_count
            },
            "checks": {name: {
                "service": check.service,
                "status": check.status,
                "response_time": check.response_time,
                "message": check.message,
                "timestamp": check.timestamp.isoformat()
            } for name, check in checks.items()}
        }

# Global monitor instance
monitor = ProductionMonitor()

def run_monitoring_cycle():
    """Run a complete monitoring cycle"""
    logger.info("Starting monitoring cycle...")
    
    checks = monitor.run_all_checks()
    report = monitor.generate_health_report(checks)
    
    # Check for alerts
    for check in checks.values():
        for alert in monitor.alerts:
            if alert.enabled and check.status in ["warning", "critical"]:
                if alert.severity == "critical" and check.status == "critical":
                    monitor.send_alert(check, alert)
                elif alert.severity == "warning" and check.status in ["warning", "critical"]:
                    monitor.send_alert(check, alert)
    
    logger.info(f"Monitoring cycle complete. Overall status: {report['overall_status']}")
    return report

if __name__ == "__main__":
    # Run monitoring cycle
    report = run_monitoring_cycle()
    print(json.dumps(report, indent=2))
