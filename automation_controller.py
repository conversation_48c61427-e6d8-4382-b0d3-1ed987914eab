#!/usr/bin/env python3
"""
Automation Controller - The Central Command for the Automated Deal Closing Machine
Orchestrates all agents, workflows, and integrations for seamless operation
"""

import asyncio
import logging
import json
import os
import signal
import sys
from typing import Dict, Any, List
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.workflows.ghl_mcp_integration import GHLMCPIntegration, start_automated_lead_processing
from agents.workflows.master_deal_orchestrator import MasterDealOrchestrator
from schedulers.follow_up_scheduler import FollowUpScheduler, run_scheduler
from api.app import app
from flask import Flask

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/automation_controller.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class AutomationController:
    """
    Central controller for the automated deal closing machine
    
    Manages:
    - GoHighLevel lead monitoring and processing
    - Follow-up scheduling and execution
    - API server for webhooks and manual triggers
    - Health monitoring and error recovery
    - Performance metrics and reporting
    """
    
    def __init__(self):
        self.ghl_integration = GHLMCPIntegration()
        self.follow_up_scheduler = FollowUpScheduler()
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.running = False
        self.tasks = []
        
        # Performance metrics
        self.metrics = {
            "leads_processed": 0,
            "offers_generated": 0,
            "follow_ups_sent": 0,
            "errors": 0,
            "start_time": None
        }
        
        logger.info("Automation Controller initialized")
    
    async def start(self):
        """Start the complete automation system"""
        logger.info("🚀 Starting Automated Deal Closing Machine")
        
        self.running = True
        self.metrics["start_time"] = datetime.now()
        
        try:
            # Start all automation components
            await self._start_all_components()
            
            # Set up signal handlers for graceful shutdown
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            logger.info("✅ All automation components started successfully")
            logger.info("🤖 Automated Deal Closing Machine is now LIVE!")
            
            # Keep the main loop running
            await self._main_loop()
            
        except Exception as e:
            logger.error(f"❌ Failed to start automation system: {e}")
            await self.stop()
    
    async def _start_all_components(self):
        """Start all automation components"""
        
        # 1. Start GHL lead monitoring
        logger.info("Starting GoHighLevel lead monitoring...")
        ghl_task = asyncio.create_task(
            self.ghl_integration.monitor_and_process_leads(continuous=True, interval_minutes=2)
        )
        self.tasks.append(ghl_task)
        
        # 2. Start follow-up scheduler
        logger.info("Starting follow-up scheduler...")
        scheduler_task = asyncio.create_task(self._run_follow_up_scheduler())
        self.tasks.append(scheduler_task)
        
        # 3. Start API server for webhooks
        logger.info("Starting API server...")
        api_task = asyncio.create_task(self._run_api_server())
        self.tasks.append(api_task)
        
        # 4. Start health monitoring
        logger.info("Starting health monitoring...")
        health_task = asyncio.create_task(self._health_monitor())
        self.tasks.append(health_task)
        
        # 5. Start metrics reporting
        logger.info("Starting metrics reporting...")
        metrics_task = asyncio.create_task(self._metrics_reporter())
        self.tasks.append(metrics_task)
    
    async def _run_follow_up_scheduler(self):
        """Run the follow-up scheduler continuously"""
        while self.running:
            try:
                logger.info("Running follow-up scheduler...")
                
                # Run the scheduler
                result = run_scheduler()
                
                if result:
                    self.metrics["follow_ups_sent"] += result.get("follow_ups_sent", 0)
                    logger.info(f"Follow-up scheduler completed: {result}")
                
                # Wait 30 minutes before next run
                await asyncio.sleep(30 * 60)
                
            except Exception as e:
                logger.error(f"Error in follow-up scheduler: {e}")
                self.metrics["errors"] += 1
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def _run_api_server(self):
        """Run the Flask API server"""
        try:
            # Run Flask app in a separate thread
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                self.executor,
                lambda: app.run(host='0.0.0.0', port=5000, debug=False)
            )
        except Exception as e:
            logger.error(f"Error running API server: {e}")
            self.metrics["errors"] += 1
    
    async def _health_monitor(self):
        """Monitor system health and restart components if needed"""
        while self.running:
            try:
                # Check if all tasks are still running
                for i, task in enumerate(self.tasks):
                    if task.done() and not task.cancelled():
                        logger.warning(f"Task {i} has stopped, checking for errors...")
                        try:
                            await task  # This will raise any exception that occurred
                        except Exception as e:
                            logger.error(f"Task {i} failed with error: {e}")
                            self.metrics["errors"] += 1
                
                # Log health status
                logger.info(f"Health check: {len([t for t in self.tasks if not t.done()])} tasks running")
                
                await asyncio.sleep(5 * 60)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(f"Error in health monitor: {e}")
                await asyncio.sleep(60)
    
    async def _metrics_reporter(self):
        """Report performance metrics periodically"""
        while self.running:
            try:
                uptime = datetime.now() - self.metrics["start_time"]
                
                logger.info("📊 AUTOMATION METRICS:")
                logger.info(f"   Uptime: {uptime}")
                logger.info(f"   Leads Processed: {self.metrics['leads_processed']}")
                logger.info(f"   Offers Generated: {self.metrics['offers_generated']}")
                logger.info(f"   Follow-ups Sent: {self.metrics['follow_ups_sent']}")
                logger.info(f"   Errors: {self.metrics['errors']}")
                
                # Save metrics to file
                metrics_file = "logs/automation_metrics.json"
                with open(metrics_file, "w") as f:
                    json.dump({
                        **self.metrics,
                        "uptime_seconds": uptime.total_seconds(),
                        "timestamp": datetime.now().isoformat()
                    }, f, indent=2)
                
                await asyncio.sleep(15 * 60)  # Report every 15 minutes
                
            except Exception as e:
                logger.error(f"Error in metrics reporter: {e}")
                await asyncio.sleep(60)
    
    async def _main_loop(self):
        """Main loop to keep the system running"""
        while self.running:
            try:
                # Wait for all tasks to complete or for shutdown signal
                await asyncio.gather(*self.tasks, return_exceptions=True)
                
                if self.running:
                    logger.warning("All tasks completed unexpectedly, restarting...")
                    await self._start_all_components()
                
            except Exception as e:
                logger.error(f"Error in main loop: {e}")
                await asyncio.sleep(5)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        asyncio.create_task(self.stop())
    
    async def stop(self):
        """Stop the automation system gracefully"""
        logger.info("🛑 Stopping Automated Deal Closing Machine...")
        
        self.running = False
        
        # Cancel all tasks
        for task in self.tasks:
            if not task.done():
                task.cancel()
        
        # Wait for tasks to complete
        if self.tasks:
            await asyncio.gather(*self.tasks, return_exceptions=True)
        
        # Shutdown executor
        self.executor.shutdown(wait=True)
        
        logger.info("✅ Automation system stopped gracefully")
    
    async def process_webhook(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process incoming webhook from GoHighLevel"""
        try:
            result = await self.ghl_integration.handle_webhook_lead(webhook_data)
            
            if result.get("success"):
                self.metrics["leads_processed"] += 1
                if "offer_generated" in result.get("actions_taken", []):
                    self.metrics["offers_generated"] += 1
            else:
                self.metrics["errors"] += 1
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing webhook: {e}")
            self.metrics["errors"] += 1
            return {"success": False, "error": str(e)}
    
    async def manual_process_lead(self, contact_id: str) -> Dict[str, Any]:
        """Manually process a specific lead"""
        try:
            result = await self.ghl_integration.process_specific_lead(contact_id)
            
            if result.get("success"):
                self.metrics["leads_processed"] += 1
                if "offer_generated" in result.get("actions_taken", []):
                    self.metrics["offers_generated"] += 1
            
            return result
            
        except Exception as e:
            logger.error(f"Error manually processing lead {contact_id}: {e}")
            return {"success": False, "error": str(e)}

# Global controller instance
automation_controller = AutomationController()

async def main():
    """Main entry point"""
    try:
        await automation_controller.start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        await automation_controller.stop()

if __name__ == "__main__":
    # Run the automation controller
    asyncio.run(main())
