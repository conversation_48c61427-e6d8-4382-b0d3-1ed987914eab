"""
Master Deal Orchestrator - The Ultimate Automated Deal Closing Machine
Coordinates all agents and workflows for seamless lead-to-deal automation
"""

import asyncio
import logging
import json
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from enum import Enum

from agents.agent_base import Agent
from agents.lead_scoring_bot import LeadScoringBot
from agents.workflows.tier_classifier import classify_lead_tier, is_hot_lead
from agents.workflows.agent_communication_workflow import execute_agent_communication
from agents.workflows.eric_notification_manager import notify_eric_hot_lead, request_eric_call
from agents.workflows.multi_tier_comping_orchestrator import MultiTierCompingOrchestrator
from agents.mao_calculator import MAOCalculator
from agents.offer_generator import generate_property_offer
from agents.offer_sender import send_property_offer
from agents.followup_bot import FollowUpBot
from agents.ghl_mcp_client import GHLMCPClient
from schedulers.follow_up_scheduler import FollowUpScheduler

logger = logging.getLogger(__name__)

class DealStage(Enum):
    """Deal pipeline stages"""
    LEAD_RECEIVED = "lead_received"
    LEAD_QUALIFIED = "lead_qualified"
    PROPERTY_ANALYZED = "property_analyzed"
    OFFER_GENERATED = "offer_generated"
    OFFER_SENT = "offer_sent"
    OFFER_UNDER_REVIEW = "offer_under_review"
    OFFER_ACCEPTED = "offer_accepted"
    OFFER_REJECTED = "offer_rejected"
    COUNTER_OFFER_RECEIVED = "counter_offer_received"
    DEAL_CLOSED = "deal_closed"
    DEAL_LOST = "deal_lost"

class MasterDealOrchestrator:
    """
    Master orchestrator that coordinates all agents for automated deal closing
    
    Workflow:
    1. Lead Ingestion & Classification
    2. Hot Lead Detection & Immediate Response
    3. Property Analysis & Comping
    4. MAO Calculation & Offer Generation
    5. Offer Delivery & Follow-up Automation
    6. Deal Tracking & Status Management
    7. Continuous Nurturing & Re-evaluation
    """
    
    def __init__(self, lead_id: str, lead_data: Dict[str, Any]):
        self.lead_id = lead_id
        self.lead_data = lead_data
        self.ghl_client = GHLMCPClient()
        self.comping_orchestrator = MultiTierCompingOrchestrator()
        self.mao_calculator = MAOCalculator()
        self.follow_up_scheduler = FollowUpScheduler()
        
        # Load workflow configuration
        self.config = self._load_workflow_config()
        
        # Initialize deal tracking
        self.deal_stage = DealStage.LEAD_RECEIVED
        self.actions_taken = []
        self.next_actions = []
        
        logger.info(f"Initialized Master Deal Orchestrator for lead {lead_id}")
    
    def _load_workflow_config(self) -> Dict[str, Any]:
        """Load workflow configuration"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), '../../config/workflows.json')
            with open(config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load workflow config: {e}")
            return {}
    
    async def execute_full_pipeline(self) -> Dict[str, Any]:
        """
        Execute the complete automated deal closing pipeline
        
        Returns:
            Dict containing pipeline results and next actions
        """
        logger.info(f"Starting full pipeline execution for lead {self.lead_id}")
        
        try:
            # Stage 1: Lead Classification & Hot Lead Detection
            classification_result = await self._stage_1_classify_and_detect()
            
            # Stage 2: Immediate Response for Hot Leads
            if classification_result.get("is_hot"):
                response_result = await self._stage_2_immediate_response()
                self.actions_taken.extend(response_result.get("actions", []))
            
            # Stage 3: Property Analysis & Comping
            if classification_result.get("tier") == 1:
                analysis_result = await self._stage_3_property_analysis()
                self.actions_taken.extend(analysis_result.get("actions", []))
                
                # Stage 4: MAO Calculation & Offer Generation
                if analysis_result.get("success"):
                    offer_result = await self._stage_4_offer_generation(analysis_result)
                    self.actions_taken.extend(offer_result.get("actions", []))
                    
                    # Stage 5: Offer Delivery & Follow-up Setup
                    if offer_result.get("offer_generated"):
                        delivery_result = await self._stage_5_offer_delivery(offer_result)
                        self.actions_taken.extend(delivery_result.get("actions", []))
            
            # Stage 6: Follow-up Automation Setup
            followup_result = await self._stage_6_followup_automation()
            self.actions_taken.extend(followup_result.get("actions", []))
            
            # Update deal stage and prepare summary
            self._update_deal_stage()
            
            return {
                "success": True,
                "lead_id": self.lead_id,
                "deal_stage": self.deal_stage.value,
                "actions_taken": self.actions_taken,
                "next_actions": self.next_actions,
                "classification": classification_result,
                "timestamp": datetime.now().isoformat(),
                "pipeline_duration": "calculated_in_implementation"
            }
            
        except Exception as e:
            logger.error(f"Pipeline execution failed for lead {self.lead_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "lead_id": self.lead_id,
                "actions_taken": self.actions_taken
            }
    
    async def _stage_1_classify_and_detect(self) -> Dict[str, Any]:
        """Stage 1: Lead Classification & Hot Lead Detection"""
        logger.info(f"Stage 1: Classifying lead {self.lead_id}")
        
        # Classify lead tier
        tier = classify_lead_tier(self.lead_data)
        is_hot = is_hot_lead(self.lead_data)
        
        # Score the lead
        scoring_bot = LeadScoringBot("lead_scoring", self.lead_id, self.lead_id)
        score_result = scoring_bot.run()
        
        result = {
            "tier": tier,
            "is_hot": is_hot,
            "score": score_result,
            "stage": DealStage.LEAD_QUALIFIED.value
        }
        
        self.deal_stage = DealStage.LEAD_QUALIFIED
        logger.info(f"Lead {self.lead_id} classified as Tier {tier}, Hot: {is_hot}")
        
        return result
    
    async def _stage_2_immediate_response(self) -> Dict[str, Any]:
        """Stage 2: Immediate Response for Hot Leads"""
        logger.info(f"Stage 2: Executing immediate response for hot lead {self.lead_id}")
        
        actions = []
        
        # Execute agent communication workflow
        comm_result = execute_agent_communication(self.lead_id, self.lead_data)
        if comm_result.get("success"):
            actions.append("immediate_communication_sent")
        
        # Notify Eric
        eric_result = notify_eric_hot_lead(self.lead_id, self.lead_data, actions)
        if eric_result.get("success"):
            actions.append("eric_notified")
        
        return {
            "success": True,
            "actions": actions,
            "communication_result": comm_result,
            "eric_notification": eric_result
        }
    
    async def _stage_3_property_analysis(self) -> Dict[str, Any]:
        """Stage 3: Property Analysis & Comping"""
        logger.info(f"Stage 3: Analyzing property for lead {self.lead_id}")
        
        actions = []
        
        try:
            # Extract property address from lead data
            address = self._extract_property_address()
            if not address:
                return {"success": False, "error": "No property address found"}
            
            # Run comprehensive comping analysis
            comping_result = await self.comping_orchestrator.get_comprehensive_comps(
                address, strategy="premium"
            )
            
            if comping_result.get("success"):
                actions.append("property_analysis_completed")
                self.deal_stage = DealStage.PROPERTY_ANALYZED
                
                return {
                    "success": True,
                    "actions": actions,
                    "comping_data": comping_result,
                    "property_address": address
                }
            else:
                return {"success": False, "error": "Comping analysis failed"}
                
        except Exception as e:
            logger.error(f"Property analysis failed: {e}")
            return {"success": False, "error": str(e)}
    
    def _extract_property_address(self) -> Optional[str]:
        """Extract property address from lead data"""
        # Try various field names that might contain the address
        address_fields = ["address", "property_address", "street_address", "full_address"]
        
        for field in address_fields:
            if field in self.lead_data and self.lead_data[field]:
                return self.lead_data[field]
        
        # Try to construct from components
        street = self.lead_data.get("street", "")
        city = self.lead_data.get("city", "")
        state = self.lead_data.get("state", "")
        zip_code = self.lead_data.get("zip", "")
        
        if street and city:
            return f"{street}, {city}, {state} {zip_code}".strip()
        
        return None
    
    async def _stage_4_offer_generation(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """Stage 4: MAO Calculation & Offer Generation"""
        logger.info(f"Stage 4: Generating offer for lead {self.lead_id}")

        actions = []

        try:
            comping_data = analysis_result.get("comping_data", {})
            property_address = analysis_result.get("property_address")

            # Calculate MAO using comping data
            mao_result = self.mao_calculator.calculate_mao(
                property_data=comping_data,
                lead_data=self.lead_data
            )

            if mao_result.get("success"):
                actions.append("mao_calculated")

                # Generate property offer
                offer_result = generate_property_offer(
                    lead_id=self.lead_id,
                    property_address=property_address,
                    mao_data=mao_result,
                    comping_data=comping_data
                )

                if offer_result.get("success"):
                    actions.append("offer_generated")
                    self.deal_stage = DealStage.OFFER_GENERATED

                    return {
                        "success": True,
                        "actions": actions,
                        "offer_generated": True,
                        "mao_result": mao_result,
                        "offer_data": offer_result
                    }

            return {"success": False, "error": "MAO calculation failed"}

        except Exception as e:
            logger.error(f"Offer generation failed: {e}")
            return {"success": False, "error": str(e)}

    async def _stage_5_offer_delivery(self, offer_result: Dict[str, Any]) -> Dict[str, Any]:
        """Stage 5: Offer Delivery & Follow-up Setup"""
        logger.info(f"Stage 5: Delivering offer for lead {self.lead_id}")

        actions = []

        try:
            offer_data = offer_result.get("offer_data", {})

            # Send offer via configured delivery methods
            delivery_methods = self.config.get("automated_deal_pipeline", {}).get("offer_management", {}).get("offer_delivery_methods", ["email"])

            for method in delivery_methods:
                if method == "email":
                    email_result = send_property_offer(
                        lead_id=self.lead_id,
                        offer_data=offer_data,
                        delivery_method="email"
                    )
                    if email_result.get("success"):
                        actions.append("offer_sent_email")

                elif method == "sms":
                    sms_result = send_property_offer(
                        lead_id=self.lead_id,
                        offer_data=offer_data,
                        delivery_method="sms"
                    )
                    if sms_result.get("success"):
                        actions.append("offer_sent_sms")

            if actions:
                self.deal_stage = DealStage.OFFER_SENT
                actions.append("offer_sent")

                # Notify Eric about offer sent
                notify_eric_hot_lead(
                    self.lead_id,
                    self.lead_data,
                    [f"Offer sent via {', '.join(delivery_methods)}"]
                )
                actions.append("eric_notified_offer_sent")

            return {
                "success": True,
                "actions": actions,
                "delivery_methods": delivery_methods
            }

        except Exception as e:
            logger.error(f"Offer delivery failed: {e}")
            return {"success": False, "error": str(e)}

    async def _stage_6_followup_automation(self) -> Dict[str, Any]:
        """Stage 6: Follow-up Automation Setup"""
        logger.info(f"Stage 6: Setting up follow-up automation for lead {self.lead_id}")

        actions = []

        try:
            # Get follow-up schedule from config
            follow_up_schedule = self.config.get("automated_deal_pipeline", {}).get("offer_management", {}).get("follow_up_schedule", [1, 3, 7, 14, 30])

            # Schedule follow-ups based on deal stage and tier
            if self.deal_stage == DealStage.OFFER_SENT:
                # Schedule offer follow-ups
                for days in follow_up_schedule:
                    self.follow_up_scheduler.schedule_follow_up(
                        lead_id=self.lead_id,
                        follow_up_date=datetime.now() + timedelta(days=days),
                        follow_up_type="offer_follow_up",
                        priority="high"
                    )
                actions.append("offer_followups_scheduled")

            else:
                # Schedule regular nurture follow-ups
                tier_2_config = self.config.get("agent_communication", {}).get("tier_2_follow_up", {})
                frequency_days = tier_2_config.get("frequency_days", 14)

                self.follow_up_scheduler.schedule_follow_up(
                    lead_id=self.lead_id,
                    follow_up_date=datetime.now() + timedelta(days=frequency_days),
                    follow_up_type="nurture_follow_up",
                    priority="medium"
                )
                actions.append("nurture_followups_scheduled")

            return {
                "success": True,
                "actions": actions,
                "follow_up_schedule": follow_up_schedule
            }

        except Exception as e:
            logger.error(f"Follow-up automation setup failed: {e}")
            return {"success": False, "error": str(e)}

    def _update_deal_stage(self):
        """Update deal stage based on actions taken"""
        if "offer_sent" in self.actions_taken:
            self.deal_stage = DealStage.OFFER_SENT
        elif "offer_generated" in self.actions_taken:
            self.deal_stage = DealStage.OFFER_GENERATED
        elif "property_analysis_completed" in self.actions_taken:
            self.deal_stage = DealStage.PROPERTY_ANALYZED

    async def handle_offer_response(self, response_type: str, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle responses to offers (acceptance, rejection, counter-offer)"""
        logger.info(f"Handling offer response: {response_type} for lead {self.lead_id}")

        if response_type == "accepted":
            self.deal_stage = DealStage.OFFER_ACCEPTED
            # Notify Eric immediately
            notify_eric_hot_lead(
                self.lead_id,
                self.lead_data,
                ["OFFER ACCEPTED! Deal ready for closing."]
            )

        elif response_type == "rejected":
            self.deal_stage = DealStage.OFFER_REJECTED
            # Move to nurture sequence

        elif response_type == "counter_offer":
            self.deal_stage = DealStage.COUNTER_OFFER_RECEIVED
            # Notify Eric for manual review
            request_eric_call(self.lead_id, "Counter-offer received, needs review")

        return {"success": True, "new_stage": self.deal_stage.value}

# Convenience function for external use
async def execute_master_pipeline(lead_id: str, lead_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Execute the master deal closing pipeline for a lead

    Args:
        lead_id: The lead ID
        lead_data: Lead data from GoHighLevel

    Returns:
        Pipeline execution results
    """
    orchestrator = MasterDealOrchestrator(lead_id, lead_data)
    return await orchestrator.execute_full_pipeline()
