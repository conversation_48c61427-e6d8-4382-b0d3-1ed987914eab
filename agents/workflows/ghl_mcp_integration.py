"""
GoHighLevel MCP Integration with Master Deal Orchestrator
Connects GHL lead data with the automated deal closing pipeline
"""

import asyncio
import logging
import json
import os
from typing import Dict, Any, List, Optional
from datetime import datetime

from agents.ghl_mcp_client import GHLMCPClient
from agents.workflows.master_deal_orchestrator import execute_master_pipeline, MasterDealOrchestrator
from agents.workflows.tier_classifier import classify_lead_tier
from agents.workflows.lead_ingestion import insert_lead_to_supabase

logger = logging.getLogger(__name__)

class GHLMCPIntegration:
    """
    Integration layer between GoHighLevel MCP and the automated deal pipeline
    """
    
    def __init__(self):
        self.ghl_client = GHLMCPClient()
        self.processed_leads = set()  # Track processed leads to avoid duplicates
        
    async def monitor_and_process_leads(self, continuous: bool = True, interval_minutes: int = 5):
        """
        Monitor GoHighLevel for new Tier 1 leads and process them automatically
        
        Args:
            continuous: Whether to run continuously or just once
            interval_minutes: How often to check for new leads (if continuous)
        """
        logger.info("Starting GHL lead monitoring and processing")
        
        while True:
            try:
                # Get Tier 1 leads from GoHighLevel
                tier_1_leads = await self._get_tier_1_leads()
                
                if tier_1_leads:
                    logger.info(f"Found {len(tier_1_leads)} Tier 1 leads to process")
                    
                    # Process each lead through the master pipeline
                    for lead in tier_1_leads:
                        lead_id = lead.get("id") or lead.get("contactId")
                        
                        if lead_id not in self.processed_leads:
                            await self._process_single_lead(lead)
                            self.processed_leads.add(lead_id)
                        else:
                            logger.debug(f"Lead {lead_id} already processed, skipping")
                
                if not continuous:
                    break
                    
                # Wait before next check
                await asyncio.sleep(interval_minutes * 60)
                
            except Exception as e:
                logger.error(f"Error in lead monitoring loop: {e}")
                if not continuous:
                    break
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def _get_tier_1_leads(self) -> List[Dict[str, Any]]:
        """Get Tier 1 leads from GoHighLevel"""
        try:
            # Search for contacts with tier 1 tag
            tier_1_contacts = self.ghl_client.search_contacts(
                query="tier 1",
                limit=50
            )
            
            if not tier_1_contacts.get("success"):
                logger.warning("Failed to retrieve Tier 1 contacts from GHL")
                return []
            
            contacts = tier_1_contacts.get("contacts", [])
            
            # Filter for recent contacts (last 24 hours) to avoid reprocessing old leads
            recent_contacts = []
            cutoff_time = datetime.now().timestamp() - (24 * 60 * 60)  # 24 hours ago
            
            for contact in contacts:
                # Check if contact was created or updated recently
                created_at = contact.get("dateAdded", 0)
                updated_at = contact.get("dateUpdated", 0)
                
                if isinstance(created_at, str):
                    created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00')).timestamp()
                if isinstance(updated_at, str):
                    updated_at = datetime.fromisoformat(updated_at.replace('Z', '+00:00')).timestamp()
                
                if max(created_at, updated_at) > cutoff_time:
                    recent_contacts.append(contact)
            
            logger.info(f"Found {len(recent_contacts)} recent Tier 1 contacts")
            return recent_contacts
            
        except Exception as e:
            logger.error(f"Error retrieving Tier 1 leads: {e}")
            return []
    
    async def _process_single_lead(self, lead_data: Dict[str, Any]):
        """Process a single lead through the master pipeline"""
        lead_id = lead_data.get("id") or lead_data.get("contactId")
        logger.info(f"Processing lead {lead_id} through master pipeline")
        
        try:
            # Enrich lead data with additional GHL information
            enriched_lead = await self._enrich_lead_data(lead_data)
            
            # Insert lead into Supabase for tracking
            supabase_lead_id = insert_lead_to_supabase(enriched_lead)
            
            if supabase_lead_id:
                enriched_lead["supabase_id"] = supabase_lead_id
            
            # Execute the master deal pipeline
            pipeline_result = await execute_master_pipeline(lead_id, enriched_lead)
            
            if pipeline_result.get("success"):
                logger.info(f"Successfully processed lead {lead_id} - Stage: {pipeline_result.get('deal_stage')}")
                logger.info(f"Actions taken: {', '.join(pipeline_result.get('actions_taken', []))}")
            else:
                logger.error(f"Pipeline failed for lead {lead_id}: {pipeline_result.get('error')}")
            
            return pipeline_result
            
        except Exception as e:
            logger.error(f"Error processing lead {lead_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def _enrich_lead_data(self, lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """Enrich lead data with additional information from GHL"""
        try:
            contact_id = lead_data.get("id") or lead_data.get("contactId")
            
            # Get contact details
            contact_details = self.ghl_client.get_contact(contact_id)
            
            if contact_details.get("success"):
                contact_info = contact_details.get("contact", {})
                
                # Merge contact details with lead data
                enriched_data = {**lead_data, **contact_info}
                
                # Get opportunities for this contact
                opportunities = self.ghl_client.get_contact_opportunities(contact_id)
                if opportunities.get("success"):
                    enriched_data["opportunities"] = opportunities.get("opportunities", [])
                
                # Get recent conversations
                conversations = self.ghl_client.get_contact_conversations(contact_id)
                if conversations.get("success"):
                    enriched_data["conversations"] = conversations.get("conversations", [])
                
                # Add processing timestamp
                enriched_data["processed_at"] = datetime.now().isoformat()
                
                return enriched_data
            
            return lead_data
            
        except Exception as e:
            logger.error(f"Error enriching lead data: {e}")
            return lead_data
    
    async def process_specific_lead(self, contact_id: str) -> Dict[str, Any]:
        """Process a specific lead by contact ID"""
        try:
            # Get contact details from GHL
            contact_result = self.ghl_client.get_contact(contact_id)
            
            if not contact_result.get("success"):
                return {"success": False, "error": "Failed to retrieve contact from GHL"}
            
            contact_data = contact_result.get("contact", {})
            
            # Check if it's a Tier 1 lead
            tier = classify_lead_tier(contact_data)
            if tier != 1:
                logger.info(f"Contact {contact_id} is not Tier 1 (Tier {tier}), processing anyway")
            
            # Process through pipeline
            return await self._process_single_lead(contact_data)
            
        except Exception as e:
            logger.error(f"Error processing specific lead {contact_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def handle_webhook_lead(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle lead from GHL webhook"""
        try:
            # Extract contact information from webhook
            contact_id = webhook_data.get("contactId") or webhook_data.get("id")
            
            if not contact_id:
                return {"success": False, "error": "No contact ID in webhook data"}
            
            # Get full contact details
            contact_result = self.ghl_client.get_contact(contact_id)
            
            if not contact_result.get("success"):
                return {"success": False, "error": "Failed to retrieve contact details"}
            
            contact_data = contact_result.get("contact", {})
            
            # Merge webhook data with contact data
            merged_data = {**webhook_data, **contact_data}
            
            # Process through pipeline
            return await self._process_single_lead(merged_data)
            
        except Exception as e:
            logger.error(f"Error handling webhook lead: {e}")
            return {"success": False, "error": str(e)}

# Convenience functions for external use
async def start_automated_lead_processing():
    """Start the automated lead processing system"""
    integration = GHLMCPIntegration()
    await integration.monitor_and_process_leads(continuous=True)

async def process_lead_by_id(contact_id: str) -> Dict[str, Any]:
    """Process a specific lead by contact ID"""
    integration = GHLMCPIntegration()
    return await integration.process_specific_lead(contact_id)

async def handle_ghl_webhook(webhook_data: Dict[str, Any]) -> Dict[str, Any]:
    """Handle incoming GHL webhook"""
    integration = GHLMCPIntegration()
    return await integration.handle_webhook_lead(webhook_data)

if __name__ == "__main__":
    # Run the automated lead processing
    asyncio.run(start_automated_lead_processing())
