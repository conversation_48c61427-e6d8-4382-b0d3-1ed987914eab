"""
Deal Analysis Orchestrator - Comprehensive Deal Analysis and ROI Calculations
Integrates all analysis components for complete deal evaluation
"""

import asyncio
import logging
import json
import os
from typing import Dict, Any, List, Optional
from datetime import datetime
from enum import Enum

from agents.agent_base import Agent
from agents.exit_strategy_bot import ExitStrategyBot
from agents.mao_calculator import MAOCalculator
from agents.workflows.multi_tier_comping_orchestrator import MultiTierCompingOrchestrator

logger = logging.getLogger(__name__)

class DealAnalysisType(Enum):
    """Types of deal analysis"""
    FLIP = "flip"
    BRRRR = "brrrr"
    WHOLESALE = "wholesale"
    BUY_HOLD = "buy_hold"
    SELLER_FINANCE = "seller_finance"

class DealAnalysisOrchestrator:
    """
    Comprehensive deal analysis orchestrator that evaluates deals across multiple strategies
    
    Analysis Components:
    1. Property Valuation & Comping
    2. Repair Cost Estimation
    3. Exit Strategy Analysis
    4. ROI Calculations
    5. Risk Assessment
    6. Market Analysis
    7. Deal Scoring & Ranking
    """
    
    def __init__(self, lead_id: str, property_data: Dict[str, Any]):
        self.lead_id = lead_id
        self.property_data = property_data
        self.comping_orchestrator = MultiTierCompingOrchestrator()
        self.mao_calculator = MAOCalculator()
        self.exit_strategy_bot = ExitStrategyBot("exit_strategy", lead_id, lead_id)
        
        # Analysis results storage
        self.analysis_results = {}
        self.deal_scores = {}
        self.recommendations = []
        
        logger.info(f"Initialized Deal Analysis Orchestrator for lead {lead_id}")
    
    async def run_comprehensive_analysis(self, strategies: List[str] = None) -> Dict[str, Any]:
        """
        Run comprehensive deal analysis across multiple strategies
        
        Args:
            strategies: List of strategies to analyze (default: all)
            
        Returns:
            Complete deal analysis results
        """
        if strategies is None:
            strategies = ["wholesale", "flip", "brrrr", "buy_hold"]
        
        logger.info(f"Starting comprehensive deal analysis for {len(strategies)} strategies")
        
        try:
            # Stage 1: Property Valuation & Comping
            comping_result = await self._stage_1_property_valuation()
            
            if not comping_result.get("success"):
                return {"success": False, "error": "Property valuation failed"}
            
            # Stage 2: Market Analysis
            market_analysis = await self._stage_2_market_analysis(comping_result)
            
            # Stage 3: Strategy-Specific Analysis
            strategy_results = {}
            for strategy in strategies:
                strategy_result = await self._analyze_strategy(strategy, comping_result, market_analysis)
                strategy_results[strategy] = strategy_result
            
            # Stage 4: Deal Scoring & Ranking
            deal_scores = self._calculate_deal_scores(strategy_results)
            
            # Stage 5: Risk Assessment
            risk_assessment = self._assess_risks(strategy_results, market_analysis)
            
            # Stage 6: Generate Recommendations
            recommendations = self._generate_recommendations(strategy_results, deal_scores, risk_assessment)
            
            # Compile final results
            final_results = {
                "success": True,
                "lead_id": self.lead_id,
                "property_address": self.property_data.get("address"),
                "analysis_timestamp": datetime.now().isoformat(),
                "property_valuation": comping_result,
                "market_analysis": market_analysis,
                "strategy_analysis": strategy_results,
                "deal_scores": deal_scores,
                "risk_assessment": risk_assessment,
                "recommendations": recommendations,
                "best_strategy": self._get_best_strategy(deal_scores),
                "overall_score": max(deal_scores.values()) if deal_scores else 0
            }
            
            # Store results for future reference
            await self._store_analysis_results(final_results)
            
            return final_results
            
        except Exception as e:
            logger.error(f"Comprehensive analysis failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def _stage_1_property_valuation(self) -> Dict[str, Any]:
        """Stage 1: Property Valuation & Comping"""
        logger.info("Stage 1: Running property valuation and comping")
        
        try:
            # Get comprehensive comps using multi-tier orchestrator
            comping_result = await self.comping_orchestrator.get_comprehensive_comps(
                self.property_data,
                strategy="premium"
            )
            
            if comping_result.get("success"):
                # Extract key valuation metrics
                arv = comping_result.get("arv_estimate", 0)
                confidence = comping_result.get("confidence_score", 0)
                comp_count = len(comping_result.get("comps", []))
                
                return {
                    "success": True,
                    "arv": arv,
                    "confidence_score": confidence,
                    "comp_count": comp_count,
                    "comping_data": comping_result,
                    "valuation_range": {
                        "low": arv * 0.9,
                        "high": arv * 1.1,
                        "conservative": arv * 0.95
                    }
                }
            else:
                return {"success": False, "error": "Comping analysis failed"}
                
        except Exception as e:
            logger.error(f"Property valuation failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def _stage_2_market_analysis(self, comping_result: Dict[str, Any]) -> Dict[str, Any]:
        """Stage 2: Market Analysis"""
        logger.info("Stage 2: Running market analysis")
        
        try:
            # Extract market data from comping results
            comps = comping_result.get("comping_data", {}).get("comps", [])
            
            if not comps:
                return {"market_trend": "unknown", "days_on_market": 0, "price_per_sqft": 0}
            
            # Calculate market metrics
            recent_sales = [comp for comp in comps if comp.get("sale_date")]
            avg_dom = sum(comp.get("days_on_market", 0) for comp in recent_sales) / len(recent_sales) if recent_sales else 0
            avg_price_per_sqft = sum(comp.get("price_per_sqft", 0) for comp in comps) / len(comps) if comps else 0
            
            # Determine market trend
            market_trend = "stable"
            if avg_dom < 30:
                market_trend = "hot"
            elif avg_dom > 90:
                market_trend = "slow"
            
            return {
                "market_trend": market_trend,
                "avg_days_on_market": avg_dom,
                "avg_price_per_sqft": avg_price_per_sqft,
                "market_activity": "high" if avg_dom < 45 else "moderate" if avg_dom < 75 else "low",
                "comp_analysis": {
                    "total_comps": len(comps),
                    "recent_sales": len(recent_sales),
                    "price_range": {
                        "min": min(comp.get("sale_price", 0) for comp in comps) if comps else 0,
                        "max": max(comp.get("sale_price", 0) for comp in comps) if comps else 0
                    }
                }
            }
            
        except Exception as e:
            logger.error(f"Market analysis failed: {e}")
            return {"market_trend": "unknown", "error": str(e)}
    
    async def _analyze_strategy(self, strategy: str, comping_result: Dict[str, Any], market_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze a specific investment strategy"""
        logger.info(f"Analyzing strategy: {strategy}")
        
        try:
            arv = comping_result.get("arv", 0)
            
            if strategy == "wholesale":
                return await self._analyze_wholesale(arv, market_analysis)
            elif strategy == "flip":
                return await self._analyze_flip(arv, market_analysis)
            elif strategy == "brrrr":
                return await self._analyze_brrrr(arv, market_analysis)
            elif strategy == "buy_hold":
                return await self._analyze_buy_hold(arv, market_analysis)
            else:
                return {"success": False, "error": f"Unknown strategy: {strategy}"}
                
        except Exception as e:
            logger.error(f"Strategy analysis failed for {strategy}: {e}")
            return {"success": False, "error": str(e)}
    
    async def _analyze_wholesale(self, arv: float, market_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze wholesale strategy"""
        # Calculate wholesale MAO (typically 70% rule minus assignment fee)
        assignment_fee = 5000  # Default assignment fee
        mao = (arv * 0.70) - assignment_fee
        
        # Estimate repair costs (conservative for wholesale)
        estimated_repairs = arv * 0.15  # 15% of ARV for repairs
        
        # Calculate potential profit
        asking_price = self.property_data.get("asking_price", arv * 0.8)
        potential_profit = mao - asking_price if asking_price else 0
        
        return {
            "success": True,
            "strategy": "wholesale",
            "mao": mao,
            "assignment_fee": assignment_fee,
            "estimated_repairs": estimated_repairs,
            "asking_price": asking_price,
            "potential_profit": potential_profit,
            "roi_percentage": (potential_profit / asking_price * 100) if asking_price > 0 else 0,
            "deal_score": self._calculate_wholesale_score(potential_profit, arv, market_analysis),
            "timeline": "30-45 days",
            "risk_level": "low"
        }
    
    async def _analyze_flip(self, arv: float, market_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze flip strategy"""
        # Calculate flip MAO (70% rule)
        estimated_repairs = arv * 0.20  # 20% of ARV for repairs
        holding_costs = arv * 0.05  # 5% for holding costs
        mao = (arv * 0.70) - estimated_repairs - holding_costs
        
        asking_price = self.property_data.get("asking_price", arv * 0.8)
        purchase_costs = asking_price * 0.03 if asking_price else 0  # 3% purchase costs
        selling_costs = arv * 0.08  # 8% selling costs
        
        total_investment = asking_price + purchase_costs + estimated_repairs + holding_costs
        net_profit = arv - total_investment - selling_costs
        
        return {
            "success": True,
            "strategy": "flip",
            "arv": arv,
            "mao": mao,
            "estimated_repairs": estimated_repairs,
            "holding_costs": holding_costs,
            "purchase_costs": purchase_costs,
            "selling_costs": selling_costs,
            "total_investment": total_investment,
            "net_profit": net_profit,
            "roi_percentage": (net_profit / total_investment * 100) if total_investment > 0 else 0,
            "deal_score": self._calculate_flip_score(net_profit, total_investment, market_analysis),
            "timeline": "6-12 months",
            "risk_level": "medium"
        }
    
    async def _analyze_brrrr(self, arv: float, market_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze BRRRR strategy"""
        # BRRRR calculations
        estimated_repairs = arv * 0.15
        asking_price = self.property_data.get("asking_price", arv * 0.8)
        total_investment = asking_price + estimated_repairs
        
        # Refinance at 75% of ARV
        refinance_amount = arv * 0.75
        cash_left_in_deal = total_investment - refinance_amount
        
        # Rental income estimation
        monthly_rent = arv * 0.01  # 1% rule
        annual_rent = monthly_rent * 12
        
        # Calculate cash flow
        monthly_expenses = monthly_rent * 0.50  # 50% rule for expenses
        monthly_cash_flow = monthly_rent - monthly_expenses
        annual_cash_flow = monthly_cash_flow * 12
        
        # Calculate returns
        cash_on_cash_return = (annual_cash_flow / cash_left_in_deal * 100) if cash_left_in_deal > 0 else 0
        
        return {
            "success": True,
            "strategy": "brrrr",
            "arv": arv,
            "total_investment": total_investment,
            "refinance_amount": refinance_amount,
            "cash_left_in_deal": cash_left_in_deal,
            "monthly_rent": monthly_rent,
            "monthly_cash_flow": monthly_cash_flow,
            "annual_cash_flow": annual_cash_flow,
            "cash_on_cash_return": cash_on_cash_return,
            "deal_score": self._calculate_brrrr_score(cash_on_cash_return, cash_left_in_deal),
            "timeline": "12-18 months to stabilize",
            "risk_level": "medium-high"
        }
    
    async def _analyze_buy_hold(self, arv: float, market_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze buy and hold strategy"""
        asking_price = self.property_data.get("asking_price", arv * 0.9)
        monthly_rent = arv * 0.01  # 1% rule
        
        # Calculate cash flow
        monthly_expenses = monthly_rent * 0.50  # 50% rule
        monthly_cash_flow = monthly_rent - monthly_expenses
        annual_cash_flow = monthly_cash_flow * 12
        
        # Calculate returns
        cap_rate = (annual_cash_flow / asking_price * 100) if asking_price > 0 else 0
        cash_on_cash_return = cap_rate  # Assuming cash purchase for simplicity
        
        return {
            "success": True,
            "strategy": "buy_hold",
            "purchase_price": asking_price,
            "monthly_rent": monthly_rent,
            "monthly_cash_flow": monthly_cash_flow,
            "annual_cash_flow": annual_cash_flow,
            "cap_rate": cap_rate,
            "cash_on_cash_return": cash_on_cash_return,
            "deal_score": self._calculate_buy_hold_score(cap_rate, monthly_cash_flow),
            "timeline": "Long-term hold",
            "risk_level": "low-medium"
        }
    
    def _calculate_wholesale_score(self, profit: float, arv: float, market_analysis: Dict[str, Any]) -> float:
        """Calculate wholesale deal score (0-100)"""
        if profit <= 0:
            return 0
        
        # Base score from profit margin
        profit_margin = profit / arv if arv > 0 else 0
        base_score = min(profit_margin * 1000, 70)  # Cap at 70 for profit alone
        
        # Market bonus
        market_bonus = 0
        if market_analysis.get("market_trend") == "hot":
            market_bonus = 20
        elif market_analysis.get("market_trend") == "stable":
            market_bonus = 10
        
        return min(base_score + market_bonus, 100)
    
    def _calculate_flip_score(self, profit: float, investment: float, market_analysis: Dict[str, Any]) -> float:
        """Calculate flip deal score (0-100)"""
        if profit <= 0 or investment <= 0:
            return 0
        
        roi = profit / investment
        base_score = min(roi * 200, 70)  # 50% ROI = 100 points, capped at 70
        
        # Market timing bonus
        market_bonus = 0
        if market_analysis.get("market_trend") == "hot":
            market_bonus = 20
        elif market_analysis.get("avg_days_on_market", 0) < 45:
            market_bonus = 15
        
        return min(base_score + market_bonus, 100)
    
    def _calculate_brrrr_score(self, cash_on_cash: float, cash_left: float) -> float:
        """Calculate BRRRR deal score (0-100)"""
        if cash_on_cash <= 0:
            return 0
        
        # Score based on cash-on-cash return
        base_score = min(cash_on_cash * 5, 70)  # 20% CoC = 100 points, capped at 70
        
        # Bonus for low cash left in deal
        if cash_left < 10000:
            base_score += 20
        elif cash_left < 25000:
            base_score += 10
        
        return min(base_score, 100)
    
    def _calculate_buy_hold_score(self, cap_rate: float, cash_flow: float) -> float:
        """Calculate buy and hold deal score (0-100)"""
        if cap_rate <= 0 or cash_flow <= 0:
            return 0
        
        # Score based on cap rate
        base_score = min(cap_rate * 10, 60)  # 10% cap rate = 100 points, capped at 60
        
        # Cash flow bonus
        if cash_flow > 500:
            base_score += 25
        elif cash_flow > 200:
            base_score += 15
        elif cash_flow > 0:
            base_score += 10
        
        return min(base_score, 100)
    
    def _calculate_deal_scores(self, strategy_results: Dict[str, Any]) -> Dict[str, float]:
        """Calculate overall deal scores for each strategy"""
        scores = {}
        for strategy, result in strategy_results.items():
            if result.get("success"):
                scores[strategy] = result.get("deal_score", 0)
            else:
                scores[strategy] = 0
        return scores
    
    def _assess_risks(self, strategy_results: Dict[str, Any], market_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Assess risks across all strategies"""
        risk_factors = []
        
        # Market risks
        if market_analysis.get("market_trend") == "slow":
            risk_factors.append("Slow market conditions may affect exit timeline")
        
        if market_analysis.get("avg_days_on_market", 0) > 90:
            risk_factors.append("High days on market indicates challenging selling conditions")
        
        # Strategy-specific risks
        for strategy, result in strategy_results.items():
            if result.get("success"):
                if strategy == "flip" and result.get("estimated_repairs", 0) > result.get("arv", 0) * 0.25:
                    risk_factors.append("High repair costs increase flip risk")
                
                if strategy == "brrrr" and result.get("cash_left_in_deal", 0) > 50000:
                    risk_factors.append("High cash left in BRRRR deal reduces returns")
        
        return {
            "risk_level": "high" if len(risk_factors) > 3 else "medium" if len(risk_factors) > 1 else "low",
            "risk_factors": risk_factors,
            "risk_score": min(len(risk_factors) * 20, 100)
        }
    
    def _generate_recommendations(self, strategy_results: Dict[str, Any], deal_scores: Dict[str, float], risk_assessment: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        best_strategy = max(deal_scores.items(), key=lambda x: x[1]) if deal_scores else ("none", 0)
        
        if best_strategy[1] > 70:
            recommendations.append(f"STRONG BUY: {best_strategy[0].title()} strategy shows excellent potential (Score: {best_strategy[1]:.0f})")
        elif best_strategy[1] > 50:
            recommendations.append(f"CONSIDER: {best_strategy[0].title()} strategy shows good potential (Score: {best_strategy[1]:.0f})")
        else:
            recommendations.append("PASS: No strategy shows strong potential for this deal")
        
        # Risk-based recommendations
        if risk_assessment.get("risk_level") == "high":
            recommendations.append("HIGH RISK: Consider additional due diligence before proceeding")
        
        # Strategy-specific recommendations
        for strategy, result in strategy_results.items():
            if result.get("success") and deal_scores.get(strategy, 0) > 60:
                if strategy == "wholesale":
                    recommendations.append(f"Wholesale: Target assignment fee of ${result.get('assignment_fee', 0):,.0f}")
                elif strategy == "flip":
                    recommendations.append(f"Flip: Budget ${result.get('estimated_repairs', 0):,.0f} for repairs")
                elif strategy == "brrrr":
                    recommendations.append(f"BRRRR: Expect ${result.get('cash_left_in_deal', 0):,.0f} cash left in deal")
        
        return recommendations
    
    def _get_best_strategy(self, deal_scores: Dict[str, float]) -> str:
        """Get the best strategy based on scores"""
        if not deal_scores:
            return "none"
        return max(deal_scores.items(), key=lambda x: x[1])[0]
    
    async def _store_analysis_results(self, results: Dict[str, Any]):
        """Store analysis results in database"""
        try:
            # This would integrate with Supabase to store results
            # For now, just log the storage action
            logger.info(f"Storing deal analysis results for lead {self.lead_id}")
            
            # TODO: Implement Supabase storage
            # supabase.table("deal_analysis").insert(results).execute()
            
        except Exception as e:
            logger.error(f"Failed to store analysis results: {e}")

# Convenience function for external use
async def run_deal_analysis(lead_id: str, property_data: Dict[str, Any], strategies: List[str] = None) -> Dict[str, Any]:
    """
    Run comprehensive deal analysis for a property
    
    Args:
        lead_id: The lead ID
        property_data: Property information
        strategies: List of strategies to analyze
        
    Returns:
        Complete deal analysis results
    """
    orchestrator = DealAnalysisOrchestrator(lead_id, property_data)
    return await orchestrator.run_comprehensive_analysis(strategies)
