
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
import json
import random
from zoneinfo import ZoneInfo

from agents.agent_base import AgentBase
from knowledge_pipeline.utils.query_kb import query_kb
from agents.ghl_client import GHLClient

# Set up logging

# NOTE: GoHighLevel MCP Integration Available
# The new GHLMCPClient provides 269+ tools for comprehensive automation.
# To upgrade this file:
# 1. Replace: from agents.ghl_client import GHLClient
# 2. With: from agents.ghl_mcp_client import GHLMCPClient
# 3. Update method calls to use enhanced MCP capabilities
# See docs/GHL_MCP_INTEGRATION.md for details

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Environment variables
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")

# Communication window in EST
EST_ZONE = ZoneInfo("America/New_York")
START_HOUR = 9
END_HOUR = 18

class FollowUpBot(AgentBase):
    """
    Bot that generates follow-up messages for leads based on their status and
    interaction history. Enforces time window restrictions and DNC/opt-out checking.
    """
    
    def __init__(self, agent_name: str, lead_id: str, entity: str, **kwargs):
        """
        Initialize the follow-up bot.
        
        Args:
            agent_name (str): The name of the agent
            lead_id (str): The ID of the lead to follow up with
            entity (str): Entity ID for agent context
            **kwargs: Additional agent parameters (currently unused by super)
        """
        super().__init__(agent_name, lead_id, entity)
        self.lead_id = lead_id
        self.ghl_client = GHLClient()
        
        # Get follow-up patterns and templates
        self.follow_up_config = self._get_follow_up_config()
    
    def _get_follow_up_config(self) -> Dict[str, Any]:
        """
        Get follow-up configuration, attempting to retrieve templates from the knowledge base.
        Falls back to hardcoded defaults if KB retrieval fails or yields no templates.
        
        Returns:
            Dict[str, Any]: Follow-up configuration
        """
        default_config = {
            "templates": {
                "initial": [
                    "Hi {first_name}, just following up on the property at {property_address}. Would you have time to discuss this week?",
                    "Hello {first_name}! I wanted to check in about {property_address}. Are you still interested in selling?",
                    "Hi there {first_name}, following up on the property at {property_address}. What would be a good time to chat?"
                ],
                "reminder": [
                    "Just a friendly reminder about the property at {property_address}. I'd love to discuss your options!",
                    "Hi {first_name}, checking in about {property_address}. Have you had a chance to consider our offer?",
                    "Hello again! Just following up on {property_address}. Would love to hear your thoughts."
                ],
                "final": [
                    "Hi {first_name}, this is my final follow-up about {property_address}. Please let me know if you'd like to discuss further.",
                    "Hello {first_name}, I wanted to reach out one last time about {property_address}. The offer is still available if you're interested.",
                    "Hi there! This is my last check-in about the property at {property_address}. Please reach out if you change your mind."
                ]
            },
            "schedule": {
                "initial": {"days": 2, "priority": "high"},
                "reminder": {"days": 5, "priority": "medium"},
                "final": {"days": 10, "priority": "low"}
            },
            "max_follow_ups": 5
        }

        kb_templates: Dict[str, List[str]] = {"initial": [], "reminder": [], "final": []}
        template_stages = ["initial", "reminder", "final"]
        found_any_kb_templates = False

        try:
            logger.info("Attempting to retrieve follow-up templates from Knowledge Base.")
            for stage in template_stages:
                tag = f"follow-up-template-{stage}"
                # General query, relying on tags for specificity
                query_text = "Retrieve follow-up message templates"
                results = query_kb(question=query_text, top_k=5, tag_filter=[tag])
                
                if results:
                    logger.info(f"Found {len(results)} templates for stage '{stage}' with tag '{tag}' in KB.")
                    for doc in results:
                        if doc and isinstance(doc.get("content"), str) and doc["content"].strip():
                            kb_templates[stage].append(doc["content"].strip())
                            found_any_kb_templates = True
                        else:
                            logger.warning(f"Empty or invalid content for a '{stage}' template from KB.")
                else:
                    logger.info(f"No templates found for stage '{stage}' with tag '{tag}' in KB.")
            
            if found_any_kb_templates:
                logger.info("Successfully retrieved templates from Knowledge Base.")
                return {
                    "templates": kb_templates,
                    "schedule": default_config["schedule"],
                    "max_follow_ups": default_config["max_follow_ups"]
                }
            else:
                logger.warning("No templates found in Knowledge Base. Falling back to default templates.")
                return default_config

        except Exception as e:
            logger.error(f"Error retrieving follow-up templates from Knowledge Base: {str(e)}. Falling back to default templates.")
            return default_config

    def is_in_dnc_list(self) -> bool:
        """
        Check if the lead is in the Do Not Contact (DNC) list.
        
        Returns:
            bool: True if the lead is in the DNC list, False otherwise
        """
        try:
            # Get lead details including DNC status
            lead_info = self.get_prompt_context()
            
            # Check for explicit DNC flag
            dnc_flag = lead_info.get("dnc", False)
            if dnc_flag:
                logger.info(f"Lead {self.lead_id} is marked as DNC")
                return True
            
            # Check for opt-out in contact notes or status
            notes = lead_info.get("notes", "").lower()
            status = lead_info.get("status", "").lower()
            
            # Look for DNC or opt-out indicators in notes and status
            dnc_indicators = [
                "do not contact", "dnc", "opted out", "opt out", 
                "remove from list", "stop texting", "stop messaging",
                "unsubscribe", "don't contact", "remove me"
            ]
            
            for indicator in dnc_indicators:
                if indicator in notes or indicator in status:
                    logger.info(f"Lead {self.lead_id} has DNC indicator in notes/status: {indicator}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking DNC status: {str(e)}")
            # Default to treating as DNC in case of error (to be safe)
            return True
    
    def is_within_allowed_time_window(self) -> bool:
        """
        Check if the current time is within the allowed communication window (9am-6pm EST).
        
        Returns:
            bool: True if within allowed window, False otherwise
        """
        # Get current time in EST
        now_est = datetime.now(tz=EST_ZONE)
        
        # Check if within 9am-6pm EST
        if START_HOUR <= now_est.hour < END_HOUR:
            # Also check for weekends
            if now_est.weekday() >= 5:  # 5 = Saturday, 6 = Sunday
                logger.info(f"Outside allowed contact time window (weekend): {now_est}")
                return False
            return True
        
        logger.info(f"Outside allowed contact time window: {now_est.hour}h EST")
        return False
    
    def generate_follow_up_message(self) -> str:
        """
        Generate a follow-up message based on lead status and interaction history.
        
        Returns:
            str: The follow-up message
        """
        try:
            # Get lead context
            lead_info = self.get_prompt_context()
            
            # Determine follow-up stage
            follow_up_count = lead_info.get("follow_up_count", 0)
            
            # Select template type based on follow-up count
            if follow_up_count == 0:
                template_type = "initial"
            elif follow_up_count < self.follow_up_config.get("max_follow_ups", 5) - 1:
                template_type = "reminder"
            else:
                template_type = "final"
            
            # Get templates for this type
            templates = self.follow_up_config.get("templates", {}).get(template_type, [])
            if not templates:
                # Fallback template
                return f"Hello {lead_info.get('first_name', 'there')}, following up about the property. Please let me know if you're interested."
            
            # Choose a random template
            template = random.choice(templates)
            
            # Format template with lead information
            message = template.format(
                first_name=lead_info.get("first_name", "there"),
                property_address=lead_info.get("property_address", "your property"),
                offer_amount=lead_info.get("offer_amount", "our offer"),
                agent_name=self.agent_name
            )
            
            return message
            
        except Exception as e:
            logger.error(f"Error generating follow-up message: {str(e)}")
            # Fallback message
            return "Hello, I'm following up about the property. Please let me know if you're interested in discussing further."
    
    def get_property_details(self) -> dict:
        """
        Get property details for the lead.
        
        Returns:
            dict: Property details
        """
        try:
            # Get lead ID
            lead_id = self.lead_id
            
            # Query Supabase for properties associated with this lead
            from agents.tools.supabase_mcp_client import SupabaseMcpClient
            supabase_client = SupabaseMcpClient()
            
            instruction = f"Get properties for lead {lead_id}"
            params = {"lead_id": lead_id}
            
            result = supabase_client.execute_instruction(instruction, params)
            
            if result.get("success", False) and result.get("data"):
                # Return the first property
                return result["data"][0]
            
            return {}
            
        except Exception as e:
            logger.error(f"Error getting property details: {str(e)}")
            return {}
    
    def get_offer_details(self) -> dict:
        """
        Get offer details for the lead.
        
        Returns:
            dict: Offer details
        """
        try:
            # Get lead ID
            lead_id = self.lead_id
            
            # Query Supabase for offers associated with this lead
            from agents.tools.supabase_mcp_client import SupabaseMcpClient
            supabase_client = SupabaseMcpClient()
            
            instruction = f"Get offers for lead {lead_id}"
            params = {"lead_id": lead_id}
            
            result = supabase_client.execute_instruction(instruction, params)
            
            if result.get("success", False) and result.get("data"):
                # Return the most recent offer
                offers = sorted(result["data"], key=lambda x: x.get("created_at", ""), reverse=True)
                return offers[0]
            
            return {}
            
        except Exception as e:
            logger.error(f"Error getting offer details: {str(e)}")
            return {}
    
    def run(self) -> str:
        """
        Generate and send a follow-up message if appropriate.
        
        Returns:
            str: Result of the follow-up attempt
        """
        # Check if within allowed time window
        if not self.is_within_allowed_time_window():
            return "Outside allowed contact window (9am-6pm EST, weekdays only)"
        
        # Check if lead is in DNC list
        if self.is_in_dnc_list():
            logger.info(f"Lead {self.lead_id} is in DNC list, skipping follow-up")
            return "Lead is in DNC list, no message sent"
        
        # Generate follow-up message
        message = self.generate_follow_up_message()
        
        # Get lead's GHL contact ID
        lead_info = self.get_prompt_context()
        contact_id = lead_info.get("ghl_contact_id")
        
        if not contact_id:
            logger.warning(f"No GHL contact ID found for lead {self.lead_id}")
            return f"Generated message but no GHL contact ID found: {message}"
        
        # Send message via GHL
        try:
            # Send message via GHL client
            self.ghl_client.send_sms(contact_id, message)
            
            # Update follow-up count in lead record
            # This would typically be done via Supabase API
            # For simplicity, we'll just log it here
            logger.info(f"Follow-up sent to lead {self.lead_id}, message: {message}")
            
            return f"Follow-up message sent: {message}"
            
        except Exception as e:
            logger.error(f"Error sending follow-up message: {str(e)}")
            return f"Error sending follow-up message: {str(e)}"
    
    def send_via_ghl(self, message: str) -> bool:
        """
        Send a message via GoHighLevel to the lead.
        
        Args:
            message (str): The message to send
            
        Returns:
            bool: True if the message was sent successfully, False otherwise
        """
        try:
            # Get lead's GHL contact ID
            lead_info = self.get_prompt_context()
            contact_id = lead_info.get("ghl_contact_id")
            
            if not contact_id:
                logger.warning(f"No GHL contact ID found for lead {self.lead_id}")
                return False
            
            # Check if within allowed time window
            if not self.is_within_allowed_time_window():
                logger.info("Outside allowed contact window, not sending message")
                return False
            
            # Check if lead is in DNC list
            if self.is_in_dnc_list():
                logger.info(f"Lead {self.lead_id} is in DNC list, not sending message")
                return False
            
            # Send message via GHL client
            result = self.ghl_client.send_sms(contact_id, message)
            
            return "error" not in result
            
        except Exception as e:
            logger.error(f"Error sending message via GHL: {str(e)}")
            return False
