import os
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
import json
import random
from zoneinfo import ZoneInfo

from agents.agent_base import Agent
from knowledge_pipeline.utils.query_kb import query_kb
from agents.ghl_client import GHLClient

# Set up logging

# NOTE: GoHighLevel MCP Integration Available
# The new GHLMCPClient provides 269+ tools for comprehensive automation.
# To upgrade this file:
# 1. Replace: from agents.ghl_client import GHLClient
# 2. With: from agents.ghl_mcp_client import GHLMCPClient
# 3. Update method calls to use enhanced MCP capabilities
# See docs/GHL_MCP_INTEGRATION.md for details

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Environment variables
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")

# Communication window in EST
EST_ZONE = ZoneInfo("America/New_York")
START_HOUR = 9
END_HOUR = 18

class DispoBot(Agent):
    """
    Bot that handles property disposition strategies and communication.
    Enforces time window restrictions and DNC/opt-out checking.
    """
    
    def __init__(self, agent_name: str, lead_id: str, entity: str, **kwargs):
        """
        Initialize the disposition bot.
        
        Args:
            agent_name (str): The name of the agent
            lead_id (str): The ID of the lead
            entity (str): Entity ID for agent context
            **kwargs: Additional agent parameters
        """
        super().__init__(agent_name, lead_id, entity, **kwargs)
        self.lead_id = lead_id
        self.property_id = kwargs.get("property_id", "")
        self.ghl_client = GHLClient()
        
        # Get disposition strategies and templates from knowledge base
        self.dispo_config = self._get_disposition_config()
    
    def _get_disposition_config(self) -> Dict[str, Any]:
        """
        Get disposition configuration from the knowledge base.
        
        Returns:
            Dict[str, Any]: Disposition configuration
        """
        try:
            # Query knowledge base for disposition configuration
            kb_query = "What are the disposition strategies and message templates for real estate properties?"
            config_context = query_kb(kb_query)
            logger.info("Retrieved disposition configuration from knowledge base")
            
            # Try to parse JSON from the context
            try:
                # Look for JSON in the context
                start_idx = config_context.find('{')
                end_idx = config_context.rfind('}') + 1
                
                if start_idx >= 0 and end_idx > start_idx:
                    json_str = config_context[start_idx:end_idx]
                    config = json.loads(json_str)
                    return config
            except (json.JSONDecodeError, AttributeError) as e:
                logger.warning(f"Could not parse disposition configuration from knowledge base: {str(e)}")
        
        except Exception as e:
            logger.error(f"Error retrieving disposition configuration: {str(e)}")
        
        # Return default configuration if knowledge base query fails
        return {
            "strategies": {
                "wholesale": {
                    "templates": [
                        "Hi {first_name}, I've found a potential buyer for {property_address}. They're offering ${offer_amount}. Would you like to proceed?",
                        "Hello {first_name}! Good news - I have a buyer interested in {property_address} at ${offer_amount}. Let me know if you'd like to discuss."
                    ],
                    "follow_up": "Let me know if you're interested in this wholesale opportunity for {property_address}. The buyer is ready to move forward."
                },
                "fix_and_flip": {
                    "templates": [
                        "Hi {first_name}, after reviewing {property_address}, we'd like to make an offer of ${offer_amount} to purchase directly. We'll handle all repairs.",
                        "Hello {first_name}! We're interested in buying {property_address} for ${offer_amount}. We'll take care of all renovations."
                    ],
                    "follow_up": "Just checking in about our offer of ${offer_amount} for {property_address}. We're ready to move quickly if you're interested."
                },
                "buy_and_hold": {
                    "templates": [
                        "Hi {first_name}, I'd like to make a long-term investment offer of ${offer_amount} for {property_address}. This would be a straightforward sale.",
                        "Hello {first_name}! We're looking to add {property_address} to our portfolio and can offer ${offer_amount}. Would you be interested?"
                    ],
                    "follow_up": "I wanted to touch base about our offer of ${offer_amount} for {property_address}. We're flexible on closing timeline."
                }
            },
            "schedule": {
                "initial_offer": {"days": 0, "priority": "high"},
                "first_follow_up": {"days": 3, "priority": "medium"},
                "second_follow_up": {"days": 7, "priority": "medium"},
                "final_follow_up": {"days": 14, "priority": "low"}
            }
        }
    
    def is_in_dnc_list(self) -> bool:
        """
        Check if the lead is in the Do Not Contact (DNC) list.
        
        Returns:
            bool: True if the lead is in the DNC list, False otherwise
        """
        try:
            # Get lead details including DNC status
            lead_info = self.get_prompt_context()
            
            # Check for explicit DNC flag
            dnc_flag = lead_info.get("dnc", False)
            if dnc_flag:
                logger.info(f"Lead {self.lead_id} is marked as DNC")
                return True
            
            # Check for opt-out in contact notes or status
            notes = lead_info.get("notes", "").lower()
            status = lead_info.get("status", "").lower()
            
            # Look for DNC or opt-out indicators in notes and status
            dnc_indicators = [
                "do not contact", "dnc", "opted out", "opt out", 
                "remove from list", "stop texting", "stop messaging",
                "unsubscribe", "don't contact", "remove me"
            ]
            
            for indicator in dnc_indicators:
                if indicator in notes or indicator in status:
                    logger.info(f"Lead {self.lead_id} has DNC indicator in notes/status: {indicator}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking DNC status: {str(e)}")
            # Default to treating as DNC in case of error (to be safe)
            return True
    
    def is_within_allowed_time_window(self) -> bool:
        """
        Check if the current time is within the allowed communication window (9am-6pm EST).
        
        Returns:
            bool: True if within allowed window, False otherwise
        """
        # Get current time in EST
        now_est = datetime.now(tz=EST_ZONE)
        
        # Check if within 9am-6pm EST
        if START_HOUR <= now_est.hour < END_HOUR:
            # Also check for weekends
            if now_est.weekday() >= 5:  # 5 = Saturday, 6 = Sunday
                logger.info(f"Outside allowed contact time window (weekend): {now_est}")
                return False
            return True
        
        logger.info(f"Outside allowed contact time window: {now_est.hour}h EST")
        return False
    
    def get_best_disposition_strategy(self) -> str:
        """
        Determine the best disposition strategy for this property.
        
        Returns:
            str: The best disposition strategy ('wholesale', 'fix_and_flip', or 'buy_and_hold')
        """
        try:
            # Get property analysis from context
            lead_info = self.get_prompt_context()
            property_analysis = lead_info.get("property_analysis", {})
            
            # Look for recommended strategy
            recommended_strategy = property_analysis.get("recommended_strategy", "")
            if recommended_strategy and recommended_strategy in self.dispo_config.get("strategies", {}):
                return recommended_strategy
            
            # If no recommended strategy, look at exit strategies
            exit_strategies = property_analysis.get("exit_strategies", [])
            if exit_strategies and isinstance(exit_strategies, list) and len(exit_strategies) > 0:
                if isinstance(exit_strategies[0], dict) and "strategy" in exit_strategies[0]:
                    top_strategy = exit_strategies[0]["strategy"]
                    if top_strategy in self.dispo_config.get("strategies", {}):
                        return top_strategy
            
            # Default to wholesale if no clear strategy found
            return "wholesale"
            
        except Exception as e:
            logger.error(f"Error determining disposition strategy: {str(e)}")
            # Default to wholesale as fallback
            return "wholesale"
    
    def generate_disposition_message(self, message_type: str = "initial") -> str:
        """
        Generate a disposition message based on property analysis and lead status.
        
        Args:
            message_type (str): Type of message to generate ('initial' or 'follow_up')
            
        Returns:
            str: The disposition message
        """
        try:
            # Get lead and property information
            lead_info = self.get_prompt_context()
            
            # Determine best disposition strategy
            strategy = self.get_best_disposition_strategy()
            strategy_config = self.dispo_config.get("strategies", {}).get(strategy, {})
            
            # Get property details and offer amount
            property_address = lead_info.get("property_address", "your property")
            offer_amount = lead_info.get("offer_amount", "")
            
            if not offer_amount:
                # Try to get from property analysis
                property_analysis = lead_info.get("property_analysis", {})
                offer_amount = property_analysis.get("mao", 0)
            
            # Format offer amount if it's a number
            if isinstance(offer_amount, (int, float)):
                offer_amount = f"{offer_amount:,.0f}"
            
            # Select template based on message type
            if message_type == "initial":
                templates = strategy_config.get("templates", [])
                if not templates:
                    # Fallback template
                    return f"Hello {lead_info.get('first_name', 'there')}, I'd like to discuss an offer for {property_address}."
                
                template = random.choice(templates)
            else:  # follow-up
                template = strategy_config.get("follow_up", "Just following up on our previous conversation about {property_address}.")
            
            # Format template with lead and property information
            message = template.format(
                first_name=lead_info.get("first_name", "there"),
                property_address=property_address,
                offer_amount=offer_amount,
                agent_name=self.agent_name
            )
            
            return message
            
        except Exception as e:
            logger.error(f"Error generating disposition message: {str(e)}")
            # Fallback message
            return "Hello, I'd like to discuss an opportunity regarding your property. Please let me know when you're available to talk."
    
    def run(self) -> str:
        """
        Generate and send a disposition message if appropriate.
        
        Returns:
            str: Result of the disposition attempt
        """
        # Check if within allowed time window
        if not self.is_within_allowed_time_window():
            return "Outside allowed contact window (9am-6pm EST, weekdays only)"
        
        # Check if lead is in DNC list
        if self.is_in_dnc_list():
            logger.info(f"Lead {self.lead_id} is in DNC list, skipping disposition")
            return "Lead is in DNC list, no message sent"
        
        # Get lead's interaction history
        lead_info = self.get_prompt_context()
        interaction_count = lead_info.get("interaction_count", 0)
        
        # Determine message type based on interaction history
        message_type = "initial" if interaction_count == 0 else "follow_up"
        
        # Generate disposition message
        message = self.generate_disposition_message(message_type)
        
        # Get lead's GHL contact ID
        contact_id = lead_info.get("ghl_contact_id")
        
        if not contact_id:
            logger.warning(f"No GHL contact ID found for lead {self.lead_id}")
            return f"Generated message but no GHL contact ID found: {message}"
        
        # Send message via GHL
        try:
            # Send message via GHL client
            self.ghl_client.send_sms(contact_id, message)
            
            # Update interaction count in lead record
            # This would typically be done via Supabase API
            # For simplicity, we'll just log it here
            logger.info(f"Disposition message sent to lead {self.lead_id}, message: {message}")
            
            return f"Disposition message sent: {message}"
            
        except Exception as e:
            logger.error(f"Error sending disposition message: {str(e)}")
            return f"Error sending disposition message: {str(e)}"
    
    def update_disposition_status(self, status: str, notes: str = "") -> bool:
        """
        Update the disposition status for a property.
        
        Args:
            status (str): New status for the property
            notes (str): Optional notes about the status change
            
        Returns:
            bool: True if the status was updated successfully, False otherwise
        """
        try:
            # In a real implementation, this would update the property status in Supabase
            # For this MVP, we'll just log the status change
            logger.info(f"Updated disposition status for property {self.property_id}: {status}")
            
            if notes:
                logger.info(f"Disposition notes: {notes}")
            
            # Get lead's GHL contact ID
            lead_info = self.get_prompt_context()
            contact_id = lead_info.get("ghl_contact_id")
            
            if contact_id:
                # Add note in GHL about status change
                note_text = f"Disposition status updated to: {status}"
                if notes:
                    note_text += f"\nNotes: {notes}"
                
                self.ghl_client.add_note(contact_id, note_text)
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating disposition status: {str(e)}")
            return False
