import os
import logging
import json
import datetime
from typing import Dict, Any, List, Optional
import uuid
from agents.ghl_client import GHLClient
from agents.offer_generator import OfferGenerator

# Set up logging

# NOTE: GoHighLevel MCP Integration Available
# The new GHLMCPClient provides 269+ tools for comprehensive automation.
# To upgrade this file:
# 1. Replace: from agents.ghl_client import GHLClient
# 2. With: from agents.ghl_mcp_client import GHLMCPClient
# 3. Update method calls to use enhanced MCP capabilities
# See docs/GHL_MCP_INTEGRATION.md for details

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OfferSender:
    """
    Sends property offers to leads via GHL and tracks sent offers.
    """
    
    def __init__(self):
        """Initialize the offer sender."""
        self.ghl_client = GHLClient()
        self.offer_generator = OfferGenerator()
        self.sent_offers_file = "data/sent_offers.json"
        self._ensure_data_directory()
    
    def _ensure_data_directory(self):
        """Ensure the data directory exists."""
        os.makedirs(os.path.dirname(self.sent_offers_file), exist_ok=True)
    
    def _load_sent_offers(self) -> List[Dict[str, Any]]:
        """
        Load previously sent offers from the tracking file.
        
        Returns:
            List[Dict[str, Any]]: List of sent offers
        """
        try:
            if os.path.exists(self.sent_offers_file):
                with open(self.sent_offers_file, "r") as f:
                    return json.load(f)
            return []
        except Exception as e:
            logger.error(f"Error loading sent offers: {str(e)}")
            return []
    
    def _save_sent_offer(self, offer_data: Dict[str, Any]):
        """
        Save a sent offer to the tracking file.
        
        Args:
            offer_data (Dict[str, Any]): Offer data to save
        """
        try:
            # Load existing offers
            sent_offers = self._load_sent_offers()
            
            # Add the new offer
            sent_offers.append(offer_data)
            
            # Save the updated list
            with open(self.sent_offers_file, "w") as f:
                json.dump(sent_offers, f, indent=2)
            
            logger.info(f"Saved offer for property {offer_data.get('property_address')} to tracking file")
        except Exception as e:
            logger.error(f"Error saving sent offer: {str(e)}")
    
    async def generate_and_send_offer(self, property_data: Dict[str, Any], contact_id: str) -> Dict[str, Any]:
        """
        Generate and send an offer for a property to a contact.
        
        Args:
            property_data (Dict[str, Any]): Property details
            contact_id (str): GHL contact ID
            
        Returns:
            Dict[str, Any]: Result of the offer sending
        """
        logger.info(f"Generating and sending offer for property at {property_data.get('address')} to contact {contact_id}")
        
        # Step 1: Generate the offer
        try:
            offer_details = await self.offer_generator.generate_offer(property_data)
            
            if not offer_details.get("success", False):
                error_msg = f"Failed to generate offer: {offer_details.get('error', 'Unknown error')}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "property_id": property_data.get("id", ""),
                    "property_address": property_data.get("address", ""),
                    "contact_id": contact_id
                }
        except Exception as e:
            error_msg = f"Error generating offer: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "property_id": property_data.get("id", ""),
                "property_address": property_data.get("address", ""),
                "contact_id": contact_id
            }
        
        # Step 2: Send the offer via GHL
        try:
            # Get contact details
            contact = self.ghl_client.get_contact(contact_id)
            
            if "error" in contact:
                error_msg = f"Failed to get contact details: {contact.get('error', 'Unknown error')}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "property_id": property_data.get("id", ""),
                    "property_address": property_data.get("address", ""),
                    "contact_id": contact_id,
                    "offer_details": offer_details
                }
            
            # Prepare email subject
            subject = f"Offer for your property at {property_data.get('address')}"
            
            # Send the offer via email
            email_result = self.ghl_client.send_email(
                contact_id=contact_id,
                subject=subject,
                body=offer_details.get("offer_message", "")
            )
            
            # Also send a text notification
            sms_message = self._generate_sms_notification(
                property_data, 
                offer_details, 
                contact.get("firstName", "")
            )
            
            sms_result = self.ghl_client.send_sms(
                contact_id=contact_id,
                body=sms_message
            )
            
            # Update contact custom field with offer amount
            self.ghl_client.update_contact_custom_field(
                contact_id=contact_id,
                field_name="Offer Amount",
                field_value=str(offer_details.get("mao", 0))
            )
            
            # Add a note about the offer
            note = f"Offer sent: ${offer_details.get('mao', 0):,.2f} for {property_data.get('address')}. " \
                   f"ARV: ${offer_details.get('arv', 0):,.2f}, " \
                   f"Repair Cost: ${offer_details.get('repair_cost', 0):,.2f}, " \
                   f"Confidence: {offer_details.get('confidence_score', 0) * 100:.0f}%"
            
            self.ghl_client.add_note(
                contact_id=contact_id,
                note=note
            )
            
            # Update opportunity stage if available
            if "opportunity_id" in property_data and "pipeline_id" in property_data:
                # Load GHL config to get the offer_sent stage ID
                ghl_config = self._load_ghl_config()
                offer_sent_stage = ghl_config.get("pipelines", {}).get("lead", {}).get("stages", {}).get("offer_sent", "")
                
                if offer_sent_stage:
                    self.ghl_client.update_opportunity_stage(
                        pipeline_id=property_data.get("pipeline_id"),
                        opportunity_id=property_data.get("opportunity_id"),
                        stage_id=offer_sent_stage
                    )
            
            # Step 3: Track the sent offer
            offer_tracking_data = {
                "offer_id": str(uuid.uuid4()),
                "property_id": property_data.get("id", ""),
                "property_address": property_data.get("address", ""),
                "contact_id": contact_id,
                "offer_amount": offer_details.get("mao", 0),
                "arv": offer_details.get("arv", 0),
                "repair_cost": offer_details.get("repair_cost", 0),
                "confidence_score": offer_details.get("confidence_score", 0),
                "sent_date": datetime.datetime.now().isoformat(),
                "expiration_date": offer_details.get("offer_expiration", ""),
                "email_sent": "error" not in email_result,
                "sms_sent": "error" not in sms_result,
                "status": "sent"
            }
            
            self._save_sent_offer(offer_tracking_data)
            
            # Return the result
            return {
                "success": True,
                "offer_id": offer_tracking_data["offer_id"],
                "property_id": property_data.get("id", ""),
                "property_address": property_data.get("address", ""),
                "contact_id": contact_id,
                "offer_amount": offer_details.get("mao", 0),
                "email_sent": "error" not in email_result,
                "sms_sent": "error" not in sms_result,
                "offer_details": offer_details
            }
            
        except Exception as e:
            error_msg = f"Error sending offer: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "property_id": property_data.get("id", ""),
                "property_address": property_data.get("address", ""),
                "contact_id": contact_id,
                "offer_details": offer_details
            }
    
    def _generate_sms_notification(self, property_data: Dict[str, Any], 
                                 offer_details: Dict[str, Any], 
                                 first_name: str) -> str:
        """
        Generate an SMS notification about the offer.
        
        Args:
            property_data (Dict[str, Any]): Property details
            offer_details (Dict[str, Any]): Offer details
            first_name (str): Contact's first name
            
        Returns:
            str: SMS message
        """
        # Load GHL config to get templates
        ghl_config = self._load_ghl_config()
        
        # Get offer notification templates
        templates = ghl_config.get("templates", {}).get("offer_notification", [])
        
        if not templates:
            # Fallback template
            template = "Hi {first_name}, we'd like to make an offer of ${offer_amount} for your property at {property_address}. Please check your email for details."
        else:
            # Randomly select a template
            import random
            template = random.choice(templates)
        
        # Format the template
        message = template.replace("{first_name}", first_name)
        message = message.replace("{property_address}", property_data.get("address", ""))
        message = message.replace("{offer_amount}", f"{offer_details.get('mao', 0):,.2f}")
        
        return message
    
    def _load_ghl_config(self) -> Dict[str, Any]:
        """
        Load the GHL configuration.
        
        Returns:
            Dict[str, Any]: GHL configuration
        """
        try:
            config_path = "config/ghl_config.json"
            with open(config_path, "r") as f:
                config = json.load(f)
            return config
        except Exception as e:
            logger.error(f"Error loading GHL config: {str(e)}")
            return {}
    
    def get_sent_offer(self, offer_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a sent offer by ID.
        
        Args:
            offer_id (str): Offer ID
            
        Returns:
            Optional[Dict[str, Any]]: Offer data or None if not found
        """
        sent_offers = self._load_sent_offers()
        
        for offer in sent_offers:
            if offer.get("offer_id") == offer_id:
                return offer
        
        return None
    
    def update_offer_status(self, offer_id: str, status: str, notes: Optional[str] = None) -> bool:
        """
        Update the status of a sent offer.
        
        Args:
            offer_id (str): Offer ID
            status (str): New status (e.g., "accepted", "rejected", "negotiating")
            notes (Optional[str]): Optional notes about the status change
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Load existing offers
            sent_offers = self._load_sent_offers()
            
            # Find the offer
            for i, offer in enumerate(sent_offers):
                if offer.get("offer_id") == offer_id:
                    # Update the status
                    sent_offers[i]["status"] = status
                    sent_offers[i]["status_updated_at"] = datetime.datetime.now().isoformat()
                    
                    if notes:
                        sent_offers[i]["status_notes"] = notes
                    
                    # Save the updated list
                    with open(self.sent_offers_file, "w") as f:
                        json.dump(sent_offers, f, indent=2)
                    
                    logger.info(f"Updated offer {offer_id} status to {status}")
                    return True
            
            logger.warning(f"Offer {offer_id} not found")
            return False
        except Exception as e:
            logger.error(f"Error updating offer status: {str(e)}")
            return False


# Helper function to generate and send an offer
async def send_property_offer(property_data: Dict[str, Any], contact_id: str) -> Dict[str, Any]:
    """
    Generate and send an offer for a property to a contact.
    
    Args:
        property_data (Dict[str, Any]): Property details
        contact_id (str): GHL contact ID
        
    Returns:
        Dict[str, Any]: Result of the offer sending
    """
    sender = OfferSender()
    return await sender.generate_and_send_offer(property_data, contact_id)
