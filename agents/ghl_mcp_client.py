"""
Enhanced GoHighLevel client that integrates with the comprehensive MCP server.
This client provides a Python interface to the 269+ MCP tools for real estate workflows.
"""

import os
import json
import logging
import requests
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class GHLMCPClient:
    """
    Enhanced GoHighLevel client using the comprehensive MCP server.
    Provides access to 269+ tools across 19 categories for complete CRM automation.
    """
    
    def __init__(self, mcp_server_url: str = "http://localhost:8000"):
        """
        Initialize the GHL MCP client.
        
        Args:
            mcp_server_url (str): URL of the running MCP server
        """
        self.mcp_server_url = mcp_server_url.rstrip('/')
        self.session = requests.Session()
        
        # Test connectivity
        self._test_connection()
    
    def _test_connection(self):
        """Test connection to the MCP server."""
        try:
            response = self.session.get(f"{self.mcp_server_url}/health", timeout=5)
            if response.status_code == 200:
                logger.info("✅ Connected to GHL MCP server")
            else:
                logger.warning(f"⚠️ MCP server responded with status {response.status_code}")
        except Exception as e:
            logger.error(f"❌ Failed to connect to MCP server: {e}")
    
    def _call_mcp_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Call an MCP tool via the HTTP server.
        
        Args:
            tool_name (str): Name of the MCP tool
            arguments (Dict[str, Any]): Tool arguments
            
        Returns:
            Dict[str, Any]: Tool response
        """
        try:
            payload = {
                "tool": tool_name,
                "arguments": arguments
            }
            
            response = self.session.post(
                f"{self.mcp_server_url}/tools/call",
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"✅ Called MCP tool {tool_name}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to call MCP tool {tool_name}: {e}")
            return {"error": str(e), "success": False}
    
    # Contact Management (31 tools)
    def create_contact(self, first_name: str, last_name: str = "", 
                      email: str = "", phone: str = "", 
                      address: str = "", custom_fields: Dict[str, Any] = None) -> Dict[str, Any]:
        """Create a new contact with comprehensive data."""
        arguments = {
            "firstName": first_name,
            "lastName": last_name,
            "email": email,
            "phone": phone,
            "address": address
        }
        
        if custom_fields:
            arguments["customFields"] = custom_fields
            
        return self._call_mcp_tool("create_contact", arguments)
    
    def search_contacts(self, query: str = "", tags: List[str] = None, 
                       custom_field_filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Search contacts with advanced filtering."""
        arguments = {"query": query}
        
        if tags:
            arguments["tags"] = tags
        if custom_field_filters:
            arguments["customFieldFilters"] = custom_field_filters
            
        return self._call_mcp_tool("search_contacts", arguments)
    
    def add_contact_tags(self, contact_id: str, tags: List[str]) -> Dict[str, Any]:
        """Add tags to a contact for organization."""
        return self._call_mcp_tool("add_contact_tags", {
            "contactId": contact_id,
            "tags": tags
        })
    
    def create_contact_task(self, contact_id: str, title: str, 
                           description: str = "", due_date: str = "") -> Dict[str, Any]:
        """Create a task for a contact."""
        arguments = {
            "contactId": contact_id,
            "title": title,
            "description": description
        }
        
        if due_date:
            arguments["dueDate"] = due_date
            
        return self._call_mcp_tool("create_contact_task", arguments)
    
    def add_contact_to_workflow(self, contact_id: str, workflow_id: str) -> Dict[str, Any]:
        """Add contact to an automation workflow."""
        return self._call_mcp_tool("add_contact_to_workflow", {
            "contactId": contact_id,
            "workflowId": workflow_id
        })
    
    # Messaging & Conversations (20 tools)
    def send_sms(self, contact_id: str, message: str, 
                 scheduled_time: str = None) -> Dict[str, Any]:
        """Send SMS with optional scheduling."""
        arguments = {
            "contactId": contact_id,
            "message": message
        }
        
        if scheduled_time:
            arguments["scheduledTime"] = scheduled_time
            
        return self._call_mcp_tool("send_sms", arguments)
    
    def send_email(self, contact_id: str, subject: str, body: str,
                   template_id: str = None, attachments: List[str] = None) -> Dict[str, Any]:
        """Send email with rich formatting and attachments."""
        arguments = {
            "contactId": contact_id,
            "subject": subject,
            "body": body
        }
        
        if template_id:
            arguments["templateId"] = template_id
        if attachments:
            arguments["attachments"] = attachments
            
        return self._call_mcp_tool("send_email", arguments)
    
    def get_conversations(self, contact_id: str) -> Dict[str, Any]:
        """Get all conversations for a contact."""
        return self._call_mcp_tool("search_conversations", {
            "contactId": contact_id
        })
    
    # Opportunity Management (10 tools)
    def create_opportunity(self, contact_id: str, pipeline_id: str, 
                          title: str, value: float = 0.0,
                          stage_id: str = None) -> Dict[str, Any]:
        """Create a sales opportunity."""
        arguments = {
            "contactId": contact_id,
            "pipelineId": pipeline_id,
            "title": title,
            "value": value
        }
        
        if stage_id:
            arguments["stageId"] = stage_id
            
        return self._call_mcp_tool("create_opportunity", arguments)
    
    def search_opportunities(self, pipeline_id: str = None, 
                           stage_id: str = None, contact_id: str = None) -> Dict[str, Any]:
        """Search opportunities with filtering."""
        arguments = {}
        
        if pipeline_id:
            arguments["pipelineId"] = pipeline_id
        if stage_id:
            arguments["stageId"] = stage_id
        if contact_id:
            arguments["contactId"] = contact_id
            
        return self._call_mcp_tool("search_opportunities", arguments)
    
    def update_opportunity_status(self, opportunity_id: str, 
                                 status: str, reason: str = "") -> Dict[str, Any]:
        """Update opportunity status (won/lost)."""
        return self._call_mcp_tool("update_opportunity_status", {
            "opportunityId": opportunity_id,
            "status": status,
            "reason": reason
        })
    
    # Calendar & Appointments (14 tools)
    def create_appointment(self, calendar_id: str, contact_id: str,
                          title: str, start_time: str, end_time: str,
                          description: str = "") -> Dict[str, Any]:
        """Create an appointment."""
        return self._call_mcp_tool("create_appointment", {
            "calendarId": calendar_id,
            "contactId": contact_id,
            "title": title,
            "startTime": start_time,
            "endTime": end_time,
            "description": description
        })
    
    def get_free_slots(self, calendar_id: str, start_date: str, 
                      end_date: str) -> Dict[str, Any]:
        """Get available appointment slots."""
        return self._call_mcp_tool("get_free_slots", {
            "calendarId": calendar_id,
            "startDate": start_date,
            "endDate": end_date
        })
    
    # Real Estate Specific Workflows
    def create_lead_workflow(self, contact_data: Dict[str, Any], 
                           property_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a complete lead workflow for real estate.
        Combines contact creation, opportunity setup, and task assignment.
        """
        results = {}
        
        # 1. Create contact
        contact_result = self.create_contact(
            first_name=contact_data.get("first_name", ""),
            last_name=contact_data.get("last_name", ""),
            email=contact_data.get("email", ""),
            phone=contact_data.get("phone", ""),
            address=property_data.get("address", ""),
            custom_fields={
                "property_address": property_data.get("address", ""),
                "lead_source": contact_data.get("source", ""),
                "tier": contact_data.get("tier", "1")
            }
        )
        results["contact"] = contact_result
        
        if "error" not in contact_result:
            contact_id = contact_result.get("contact", {}).get("id")
            
            # 2. Create opportunity
            opportunity_result = self.create_opportunity(
                contact_id=contact_id,
                pipeline_id=property_data.get("pipeline_id", ""),
                title=f"Property: {property_data.get('address', '')}",
                value=property_data.get("estimated_value", 0.0)
            )
            results["opportunity"] = opportunity_result
            
            # 3. Add relevant tags
            tags = ["real-estate-lead"]
            if property_data.get("property_type"):
                tags.append(f"property-{property_data['property_type']}")
            if contact_data.get("tier"):
                tags.append(f"tier-{contact_data['tier']}")
                
            tag_result = self.add_contact_tags(contact_id, tags)
            results["tags"] = tag_result
            
            # 4. Create follow-up task
            task_result = self.create_contact_task(
                contact_id=contact_id,
                title="Initial property analysis",
                description=f"Analyze property at {property_data.get('address', '')} and prepare offer",
                due_date=(datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
            )
            results["task"] = task_result
        
        return results
    
    def send_offer_notification(self, contact_id: str, offer_amount: float,
                               property_address: str, offer_details: Dict[str, Any] = None) -> Dict[str, Any]:
        """Send offer notification via SMS and email."""
        results = {}
        
        # SMS notification
        sms_message = f"Hi! We'd like to make an offer of ${offer_amount:,.2f} for your property at {property_address}. Let me know if you'd like to discuss!"
        
        sms_result = self.send_sms(contact_id, sms_message)
        results["sms"] = sms_result
        
        # Email notification with details
        email_subject = f"Property Offer - ${offer_amount:,.2f} for {property_address}"
        email_body = f"""
        <h2>Property Offer</h2>
        <p>We're pleased to present an offer for your property:</p>
        <ul>
            <li><strong>Property:</strong> {property_address}</li>
            <li><strong>Offer Amount:</strong> ${offer_amount:,.2f}</li>
        </ul>
        """
        
        if offer_details:
            email_body += "<h3>Offer Details:</h3><ul>"
            for key, value in offer_details.items():
                email_body += f"<li><strong>{key.replace('_', ' ').title()}:</strong> {value}</li>"
            email_body += "</ul>"
        
        email_body += "<p>Please let us know if you'd like to discuss this offer further.</p>"
        
        email_result = self.send_email(contact_id, email_subject, email_body)
        results["email"] = email_result
        
        return results
