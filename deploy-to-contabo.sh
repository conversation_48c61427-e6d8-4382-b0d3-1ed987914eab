#!/bin/bash
# Deploy AI-OS to Contabo VPS

set -e

# Get user input
read -p "Enter your Contabo VPS IP address: " VPS_IP
read -p "Enter your domain (or press Enter to use IP): " DOMAIN
read -p "Enter SSH username [root]: " SSH_USER
SSH_USER=${SSH_USER:-root}

if [ -z "$DOMAIN" ]; then
    DOMAIN=$VPS_IP
    USE_SSL=false
else
    USE_SSL=true
fi

echo "🚀 Deploying AI-OS to Contabo VPS"
echo "================================="
echo "VPS IP: $VPS_IP"
echo "Domain: $DOMAIN" 
echo "SSH User: $SSH_USER"
echo "SSL: $USE_SSL"
echo ""

# 1. Copy setup script to server
echo "📁 Copying setup script to server..."
scp contabo-setup.sh $SSH_USER@$VPS_IP:/tmp/

# 2. Run setup script on server
echo "🔧 Running server setup..."
ssh $SSH_USER@$VPS_IP "chmod +x /tmp/contabo-setup.sh && /tmp/contabo-setup.sh"

# 3. Sync project files (excluding large directories)
echo "📦 Syncing project files..."
rsync -avz --progress \
    --exclude 'node_modules' \
    --exclude '.git' \
    --exclude 'venv' \
    --exclude '*.log' \
    --exclude 'logs/' \
    --exclude 'data/' \
    . $SSH_USER@$VPS_IP:/opt/AI-OS/

# 4. Copy environment file
echo "🔑 Setting up environment..."
scp .env $SSH_USER@$VPS_IP:/opt/AI-OS/

# 5. Create Caddyfile for reverse proxy
echo "🌐 Setting up reverse proxy..."
if [ "$USE_SSL" = true ]; then
cat > /tmp/Caddyfile << EOF
$DOMAIN {
    # Retool dashboard
    reverse_proxy /retool/* localhost:3000

    # n8n webhooks and interface
    reverse_proxy /webhook/* localhost:5678
    reverse_proxy /n8n/* localhost:5678

    # AI-OS API
    reverse_proxy /api/* localhost:5002

    # Default to n8n for root path
    reverse_proxy /* localhost:5678

    encode gzip

    log {
        output file /var/log/caddy/aios.log
        level INFO
    }
}
EOF
else
cat > /tmp/Caddyfile << EOF
:80 {
    # Retool dashboard
    reverse_proxy /retool/* localhost:3000

    # n8n webhooks and interface
    reverse_proxy /webhook/* localhost:5678
    reverse_proxy /n8n/* localhost:5678

    # AI-OS API
    reverse_proxy /api/* localhost:5002

    # Default to n8n for root path
    reverse_proxy /* localhost:5678

    encode gzip

    log {
        output file /var/log/caddy/aios.log
        level INFO
    }
}
EOF
fi

scp /tmp/Caddyfile $SSH_USER@$VPS_IP:/opt/AI-OS/

# 6. Deploy and start services
echo "🐳 Starting services on server..."
ssh $SSH_USER@$VPS_IP << 'EOF'
cd /opt/AI-OS

# Update environment for production
if [ -f .env ]; then
    # Update webhook URLs in environment
    sed -i "s|http://localhost:5678|https://DOMAIN_PLACEHOLDER|g" .env
    sed -i "s|localhost|DOMAIN_PLACEHOLDER|g" .env

    # Update Retool hostname for production
    sed -i "s|RETOOL_HOSTNAME=.*|RETOOL_HOSTNAME=https://DOMAIN_PLACEHOLDER|g" .env
fi

# Start Docker services
docker-compose pull
docker-compose up -d

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 30

# Start Caddy
mkdir -p /var/log/caddy
caddy stop 2>/dev/null || true
caddy start --config Caddyfile

# Check service status
echo "📊 Service Status:"
docker-compose ps

echo "🧪 Testing endpoints..."
sleep 10
curl -f http://localhost:5002/api/v1/health && echo "✅ API healthy"
curl -f http://localhost:5678/healthz && echo "✅ n8n healthy"
curl -f http://localhost:3000/api/checkHealth && echo "✅ Retool healthy"

EOF

# 7. Update environment with actual domain
if [ "$USE_SSL" = true ]; then
    WEBHOOK_URL="https://$DOMAIN"
else
    WEBHOOK_URL="http://$DOMAIN"
fi

ssh $SSH_USER@$VPS_IP "cd /opt/AI-OS && sed -i 's|DOMAIN_PLACEHOLDER|$DOMAIN|g' .env"

echo ""
echo "✅ Deployment complete!"
echo ""
echo "🌐 Access URLs:"
echo "   AI-OS API: $WEBHOOK_URL/api/v1/health"
echo "   n8n Interface: $WEBHOOK_URL/n8n"
echo "   Retool Dashboard: $WEBHOOK_URL/retool"
echo "   Webhook URL: $WEBHOOK_URL/webhook/ghl/lead"
echo ""
echo "📋 Next steps:"
echo "1. Test the endpoints above"
echo "2. Update GHL webhooks to use: $WEBHOOK_URL/webhook/ghl/lead"
echo "3. Monitor logs: ssh $SSH_USER@$VPS_IP 'docker-compose logs -f'"

# Clean up temp files
rm -f /tmp/Caddyfile