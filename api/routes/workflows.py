#!/usr/bin/env python3
"""
Workflow API Routes

This module provides API endpoints for triggering various workflows including:
- Comping workflow
- Follow-up automation
- MAO calculation
- Property analysis
"""

import os
import logging
import json
from datetime import datetime
from typing import Dict, Any, List, Optional

from flask import Blueprint, request, jsonify
from flask_cors import cross_origin

# Import workflow modules
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from agents.workflows.comping_workflow import run_comping_workflow
from agents.workflows.intelligent_followup import create_intelligent_followup
from agents.workflows.agent_communication_workflow import execute_agent_communication
from agents.workflows.eric_notification_manager import notify_eric_hot_lead, request_eric_call
from agents.workflows.tier_classifier import is_hot_lead, classify_lead_tier
from agents.workflows.ghl_mcp_integration import handle_ghl_webhook, process_lead_by_id
from agents.workflows.master_deal_orchestrator import execute_master_pipeline
from agents.mao_calculator import MAOCalculator
from schedulers.follow_up_scheduler import FollowUpScheduler

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create Blueprint
workflows_bp = Blueprint('workflows', __name__, url_prefix='/api/v1/workflows')

@workflows_bp.route('/comping', methods=['POST'])
@cross_origin()
def trigger_comping_workflow():
    """
    Trigger comping workflow for a lead

    Expected JSON payload:
    {
        "lead_id": "string",
        "property_address": "string" (optional),
        "property_details": object (optional)
    }

    Returns:
        JSON response with comping results
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        lead_id = data.get('lead_id')
        if not lead_id:
            return jsonify({
                "success": False,
                "error": "lead_id is required"
            }), 400

        property_address = data.get('property_address', '')
        property_details = data.get('property_details', {})

        logger.info(f"Triggering comping workflow for lead: {lead_id}")

        # Run comping workflow
        result = run_comping_workflow(
            lead_id=lead_id,
            property_address=property_address,
            property_details=property_details
        )

        return jsonify({
            "success": True,
            "workflow": "comping",
            "lead_id": lead_id,
            "result": result,
            "timestamp": datetime.utcnow().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"Error in trigger_comping_workflow: {str(e)}")
        return jsonify({
            "success": False,
            "workflow": "comping",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500

@workflows_bp.route('/followup', methods=['POST'])
@cross_origin()
def trigger_followup_workflow():
    """
    Trigger intelligent follow-up workflow

    Expected JSON payload:
    {
        "lead_id": "string",
        "priority": "string" (optional),
        "tier": integer (optional)
    }

    Returns:
        JSON response with follow-up setup results
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        lead_id = data.get('lead_id')
        if not lead_id:
            return jsonify({
                "success": False,
                "error": "lead_id is required"
            }), 400

        priority = data.get('priority', 'medium')
        tier = data.get('tier', 2)

        logger.info(f"Triggering follow-up workflow for lead: {lead_id}")

        # Create intelligent follow-up
        result = create_intelligent_followup(lead_id, priority, tier)

        return jsonify({
            "success": True,
            "workflow": "followup",
            "lead_id": lead_id,
            "result": result,
            "timestamp": datetime.utcnow().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"Error in trigger_followup_workflow: {str(e)}")
        return jsonify({
            "success": False,
            "workflow": "followup",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500

@workflows_bp.route('/mao-calculation', methods=['POST'])
@cross_origin()
def trigger_mao_calculation():
    """
    Trigger MAO (Maximum Allowable Offer) calculation

    Expected JSON payload:
    {
        "lead_id": "string",
        "property_value": number,
        "repair_costs": number (optional),
        "holding_costs": number (optional)
    }

    Returns:
        JSON response with MAO calculation results
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        lead_id = data.get('lead_id')
        property_value = data.get('property_value')

        if not lead_id or property_value is None:
            return jsonify({
                "success": False,
                "error": "lead_id and property_value are required"
            }), 400

        repair_costs = data.get('repair_costs', 0)
        holding_costs = data.get('holding_costs', 0)

        logger.info(f"Calculating MAO for lead: {lead_id}")

        # Calculate MAO
        calculator = MAOCalculator(lead_id=lead_id)
        result = calculator.calculate_mao(
            arv=property_value,
            repair_costs=repair_costs,
            holding_costs=holding_costs
        )

        return jsonify({
            "success": True,
            "workflow": "mao_calculation",
            "lead_id": lead_id,
            "result": result,
            "timestamp": datetime.utcnow().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"Error in trigger_mao_calculation: {str(e)}")
        return jsonify({
            "success": False,
            "workflow": "mao_calculation",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500

@workflows_bp.route('/property-analysis', methods=['POST'])
@cross_origin()
def trigger_property_analysis():
    """
    Trigger comprehensive property analysis

    Expected JSON payload:
    {
        "lead_id": "string",
        "property_address": "string",
        "analysis_type": "string" (optional)
    }

    Returns:
        JSON response with property analysis results
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        lead_id = data.get('lead_id')
        property_address = data.get('property_address')

        if not lead_id or not property_address:
            return jsonify({
                "success": False,
                "error": "lead_id and property_address are required"
            }), 400

        analysis_type = data.get('analysis_type', 'comprehensive')

        logger.info(f"Triggering property analysis for lead: {lead_id}")

        # This would integrate with property analysis agents
        # For now, return a placeholder response
        result = {
            "property_address": property_address,
            "analysis_type": analysis_type,
            "status": "analysis_initiated",
            "estimated_completion": "15-30 minutes"
        }

        return jsonify({
            "success": True,
            "workflow": "property_analysis",
            "lead_id": lead_id,
            "result": result,
            "timestamp": datetime.utcnow().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"Error in trigger_property_analysis: {str(e)}")
        return jsonify({
            "success": False,
            "workflow": "property_analysis",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500

@workflows_bp.route('/batch-trigger', methods=['POST'])
@cross_origin()
def batch_trigger_workflows():
    """
    Trigger multiple workflows for multiple leads

    Expected JSON payload:
    {
        "workflows": [
            {
                "workflow_type": "string",
                "lead_id": "string",
                "parameters": object
            }
        ]
    }

    Returns:
        JSON response with batch execution results
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        workflows = data.get('workflows', [])
        if not workflows or not isinstance(workflows, list):
            return jsonify({
                "success": False,
                "error": "workflows must be a non-empty list"
            }), 400

        logger.info(f"Batch triggering {len(workflows)} workflows")

        results = []
        for workflow_config in workflows:
            try:
                workflow_type = workflow_config.get('workflow_type')
                lead_id = workflow_config.get('lead_id')
                parameters = workflow_config.get('parameters', {})

                if not workflow_type or not lead_id:
                    results.append({
                        "success": False,
                        "error": "workflow_type and lead_id are required",
                        "workflow_config": workflow_config
                    })
                    continue

                # Route to appropriate workflow
                if workflow_type == 'comping':
                    result = run_comping_workflow(lead_id=lead_id, **parameters)
                elif workflow_type == 'followup':
                    result = create_intelligent_followup(lead_id, **parameters)
                elif workflow_type == 'mao_calculation':
                    calculator = MAOCalculator(lead_id=lead_id)
                    result = calculator.calculate_mao(**parameters)
                else:
                    result = {
                        "success": False,
                        "error": f"Unknown workflow type: {workflow_type}"
                    }

                results.append({
                    "success": True,
                    "workflow_type": workflow_type,
                    "lead_id": lead_id,
                    "result": result
                })

            except Exception as e:
                results.append({
                    "success": False,
                    "error": str(e),
                    "workflow_config": workflow_config
                })

        # Calculate summary
        successful = sum(1 for r in results if r.get('success'))
        total = len(results)

        return jsonify({
            "success": True,
            "total_workflows": total,
            "successful_workflows": successful,
            "failed_workflows": total - successful,
            "results": results,
            "timestamp": datetime.utcnow().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"Error in batch_trigger_workflows: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500

@workflows_bp.route('/status/<workflow_id>', methods=['GET'])
@cross_origin()
def get_workflow_status(workflow_id):
    """
    Get status of a running workflow

    Returns:
        JSON response with workflow status
    """
    try:
        logger.info(f"Getting status for workflow: {workflow_id}")

        # This would integrate with a workflow tracking system
        # For now, return a placeholder response
        result = {
            "workflow_id": workflow_id,
            "status": "running",
            "progress": "50%",
            "estimated_completion": "10 minutes",
            "last_updated": datetime.utcnow().isoformat()
        }

        return jsonify({
            "success": True,
            "workflow_status": result,
            "timestamp": datetime.utcnow().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"Error in get_workflow_status: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500

@workflows_bp.route('/hot-lead-processing', methods=['POST'])
@cross_origin()
def trigger_hot_lead_processing():
    """
    Trigger hot lead processing workflow

    Expected JSON payload:
    {
        "lead_id": "string",
        "priority": "string" (optional),
        "tier": integer (optional),
        "is_hot_lead": boolean (optional)
    }

    Returns:
        JSON response with hot lead processing results
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        lead_id = data.get('lead_id')
        if not lead_id:
            return jsonify({
                "success": False,
                "error": "lead_id is required"
            }), 400

        priority = data.get('priority', 'critical')
        tier = data.get('tier', 1)
        is_hot = data.get('is_hot_lead', True)

        logger.info(f"Triggering hot lead processing for lead: {lead_id}")

        # Get lead data (in a real implementation, this would fetch from database)
        lead_data = {
            "id": lead_id,
            "tier": tier,
            "priority": priority,
            "is_hot_lead": is_hot,
            # Add other lead data as needed
        }

        # Execute agent communication workflow
        comm_result = execute_agent_communication(lead_id, lead_data)

        # Notify Eric about the hot lead
        actions_taken = []
        if comm_result.get("success"):
            for result in comm_result.get("results", []):
                if result.get("success"):
                    actions_taken.append(f"{result['message_type'].upper()} sent using {result['template_used']}")

        eric_notification_result = notify_eric_hot_lead(lead_id, lead_data, actions_taken)

        # Compile results
        result = {
            "lead_id": lead_id,
            "agent_communication": comm_result,
            "eric_notification": eric_notification_result,
            "actions_taken": actions_taken,
            "status": "hot_lead_processing_complete"
        }

        return jsonify({
            "success": True,
            "workflow": "hot_lead_processing",
            "lead_id": lead_id,
            "result": result,
            "timestamp": datetime.utcnow().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"Error in trigger_hot_lead_processing: {str(e)}")
        return jsonify({
            "success": False,
            "workflow": "hot_lead_processing",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500

@workflows_bp.route('/eric-call-request', methods=['POST'])
@cross_origin()
def request_eric_call_endpoint():
    """
    Request Eric to call an agent

    Expected JSON payload:
    {
        "lead_id": "string",
        "call_reason": "string",
        "priority": "string" (optional)
    }

    Returns:
        JSON response with call request results
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        lead_id = data.get('lead_id')
        call_reason = data.get('call_reason')

        if not lead_id or not call_reason:
            return jsonify({
                "success": False,
                "error": "lead_id and call_reason are required"
            }), 400

        priority = data.get('priority', 'high')

        logger.info(f"Requesting Eric call for lead: {lead_id}")

        # Get lead data (in a real implementation, this would fetch from database)
        lead_data = {
            "id": lead_id,
            "call_reason": call_reason,
            # Add other lead data as needed
        }

        # Request Eric call
        result = request_eric_call(lead_id, lead_data, call_reason, priority)

        return jsonify({
            "success": result,
            "workflow": "eric_call_request",
            "lead_id": lead_id,
            "call_reason": call_reason,
            "priority": priority,
            "timestamp": datetime.utcnow().isoformat()
        }), 200 if result else 500

    except Exception as e:
        logger.error(f"Error in request_eric_call_endpoint: {str(e)}")
        return jsonify({
            "success": False,
            "workflow": "eric_call_request",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500

@workflows_bp.route('/health', methods=['GET'])
@cross_origin()
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "service": "workflows_api",
        "timestamp": datetime.utcnow().isoformat()
    }), 200

@workflows_bp.route('/ghl-webhook', methods=['POST'])
@cross_origin()
async def ghl_webhook():
    """
    GoHighLevel webhook endpoint for automated lead processing
    Triggers the complete deal closing pipeline
    """
    try:
        webhook_data = request.get_json()

        if not webhook_data:
            return jsonify({
                "status": "error",
                "message": "No data received"
            }), 400

        logger.info(f"Received GHL webhook: {webhook_data}")

        # Process through the master pipeline
        result = await handle_ghl_webhook(webhook_data)

        if result.get("success"):
            return jsonify({
                "status": "success",
                "message": "Lead processed successfully",
                "lead_id": result.get("lead_id"),
                "deal_stage": result.get("deal_stage"),
                "actions_taken": result.get("actions_taken", []),
                "timestamp": datetime.utcnow().isoformat()
            })
        else:
            return jsonify({
                "status": "error",
                "message": result.get("error", "Processing failed"),
                "timestamp": datetime.utcnow().isoformat()
            }), 500

    except Exception as e:
        logger.error(f"Error processing GHL webhook: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Internal error: {str(e)}",
            "timestamp": datetime.utcnow().isoformat()
        }), 500

@workflows_bp.route('/process-lead/<contact_id>', methods=['POST'])
@cross_origin()
async def manual_process_lead(contact_id):
    """
    Manually trigger lead processing for a specific contact
    """
    try:
        logger.info(f"Manual processing requested for contact: {contact_id}")

        # Process the specific lead
        result = await process_lead_by_id(contact_id)

        if result.get("success"):
            return jsonify({
                "status": "success",
                "message": "Lead processed successfully",
                "contact_id": contact_id,
                "deal_stage": result.get("deal_stage"),
                "actions_taken": result.get("actions_taken", []),
                "timestamp": datetime.utcnow().isoformat()
            })
        else:
            return jsonify({
                "status": "error",
                "message": result.get("error", "Processing failed"),
                "contact_id": contact_id,
                "timestamp": datetime.utcnow().isoformat()
            }), 500

    except Exception as e:
        logger.error(f"Error manually processing lead {contact_id}: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Internal error: {str(e)}",
            "contact_id": contact_id,
            "timestamp": datetime.utcnow().isoformat()
        }), 500
