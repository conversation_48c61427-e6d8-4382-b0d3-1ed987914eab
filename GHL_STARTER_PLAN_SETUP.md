# 🚀 GoHighLevel MCP Setup - Starter Plan ($97/month)

## ✅ Perfect Setup for Your Plan

You're on the **Starter Plan** which means you should use the **Location API Key** approach, not Private Integrations OAuth. This is actually simpler and works perfectly for the MCP integration!

## 🔑 Step 1: Get Your Location API Key

### **Navigate to API Settings:**
```
1. Go to your GoHighLevel sub-account (location)
2. Navigate to: Settings → Company → API Key
3. You'll see both:
   - Location API Key (this is what you need!)
   - Location ID (you need this too!)
```

### **Copy Both Values:**
- ✅ **Location API Key** (long string starting with letters/numbers)
- ✅ **Location ID** (shorter string, also on the same page)

## 🔧 Step 2: Set Environment Variables

```bash
# Add these to your .env file or export them
export GHL_API_KEY="your_location_api_key_here"
export GHL_LOCATION_ID="your_location_id_here"
```

## 🚀 Step 3: Run Automated Setup

```bash
# This will configure everything for your Starter Plan
python3 scripts/setup_ghl_mcp.py
```

## 🧪 Step 4: Test Your Integration

```bash
# Test the full integration
python3 scripts/test_ghl_mcp_integration.py
```

## 🎯 What You Get with Starter Plan + Location API Key

### **✅ Full Access To:**
- **Contact Management**: Create, update, search, tag contacts
- **Messaging**: Send SMS/Email (if Twilio/Mailgun configured)
- **Opportunities**: Create and move through pipelines
- **Tasks & Notes**: Add tasks and notes to contacts
- **Custom Fields**: Work with custom contact data
- **Workflows**: Trigger automation workflows via tags

### **🏠 Perfect for Real Estate:**
- **Lead Processing**: Automated contact creation and tagging
- **Pipeline Management**: Move leads through your sales process
- **Follow-up Automation**: Trigger workflows and send messages
- **Property Data**: Store custom fields for property information
- **Offer Tracking**: Create opportunities for each property offer

## 🔧 Available MCP Tools (Starter Plan)

With your Location API Key, you'll have access to these core tools:

### **Contact Tools:**
- `create_contact` - Create new leads
- `search_contacts` - Find existing contacts
- `update_contact` - Update contact information
- `add_contact_tags` - Tag contacts to trigger workflows
- `create_contact_task` - Add follow-up tasks
- `create_contact_note` - Add notes and updates

### **Messaging Tools:**
- `send_sms` - Send SMS messages
- `send_email` - Send email messages
- `get_conversations` - View message history

### **Opportunity Tools:**
- `create_opportunity` - Create deals in pipeline
- `search_opportunities` - Find existing opportunities
- `update_opportunity` - Move through pipeline stages

### **Workflow Tools:**
- `add_contact_to_workflow` - Trigger automation sequences
- `remove_contact_from_workflow` - Stop automation

## 🏠 Real Estate Workflow Example

Here's what you can automate with your Starter Plan setup:

```python
from agents.ghl_mcp_client import GHLMCPClient

# Initialize with Location API Key
ghl = GHLMCPClient()

# Complete lead workflow
def process_real_estate_lead(lead_data):
    # 1. Create contact
    contact = ghl.create_contact(
        first_name=lead_data["first_name"],
        last_name=lead_data["last_name"],
        email=lead_data["email"],
        phone=lead_data["phone"],
        custom_fields={
            "property_address": lead_data["property_address"],
            "lead_source": lead_data["source"]
        }
    )
    
    # 2. Tag for workflow automation
    ghl.add_contact_tags(contact["id"], ["new-lead", "tier-1"])
    
    # 3. Create opportunity
    ghl.create_opportunity(
        contact_id=contact["id"],
        pipeline_id="your_pipeline_id",
        title=f"Property: {lead_data['property_address']}",
        value=lead_data.get("estimated_value", 0)
    )
    
    # 4. Send welcome message
    ghl.send_sms(
        contact_id=contact["id"],
        message=f"Hi {lead_data['first_name']}! Thanks for your interest in selling {lead_data['property_address']}. I'm analyzing your property value now."
    )
    
    # 5. Create follow-up task
    ghl.create_contact_task(
        contact_id=contact["id"],
        title="Property Analysis",
        description=f"Complete market analysis for {lead_data['property_address']}"
    )
    
    return contact
```

## 🎯 Claude Desktop Integration

Your Claude Desktop config will look like this:

```json
{
  "mcpServers": {
    "gohighlevel-starter": {
      "command": "node",
      "args": ["/absolute/path/to/ghl-mcp-server/dist/server.js"],
      "env": {
        "GHL_API_KEY": "your_location_api_key",
        "GHL_BASE_URL": "https://rest.gohighlevel.com/v1",
        "GHL_LOCATION_ID": "your_location_id",
        "NODE_ENV": "production"
      }
    }
  }
}
```

## 🚀 Deployment Options

### **Local Development:**
```bash
./scripts/deploy_ghl_mcp.sh
./start-ghl-mcp.sh
```

### **Docker:**
```bash
docker-compose -f docker-compose.ghl-mcp.yml up -d
```

### **Cloud (Vercel/Railway):**
- Deploy the `ghl-mcp-server` directory
- Set environment variables in the platform dashboard

## 🎉 Success Metrics for Starter Plan

With this setup, you'll achieve:

- ✅ **Automated Lead Processing** - No more manual contact creation
- ✅ **Workflow Triggers** - Tag-based automation sequences
- ✅ **Pipeline Management** - Automated opportunity tracking
- ✅ **Multi-Channel Messaging** - SMS + Email automation
- ✅ **Task Management** - Automated follow-up scheduling
- ✅ **Claude Desktop Integration** - AI-powered CRM management

## 🔥 Why This Is Perfect for Starter Plan

1. **No OAuth Complexity** - Simple Location API Key approach
2. **Full CRM Access** - All the tools you need for real estate
3. **Workflow Integration** - Trigger your existing GHL automations
4. **Cost Effective** - Maximum automation on your current plan
5. **Scalable** - Easy to upgrade when you grow

## 🆘 Troubleshooting

### **"API Key Invalid":**
- Double-check you copied the Location API Key (not Location ID)
- Verify you're using the key from Settings → Company → API Key

### **"Location Not Found":**
- Ensure Location ID matches exactly
- Check you're in the correct sub-account

### **"Permission Denied":**
- Some features require Twilio/Mailgun setup for messaging
- Verify your GHL account has the features enabled

## 🎯 You're All Set!

This setup gives you **professional-grade automation** on your Starter Plan. You'll have the core tools needed for real estate lead processing, pipeline management, and workflow automation.

**The Location API Key approach is actually cleaner and more direct than OAuth - you're getting the best possible setup for your plan level!** 🚀
