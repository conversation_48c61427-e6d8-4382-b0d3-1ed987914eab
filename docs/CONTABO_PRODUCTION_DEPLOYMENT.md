# Contabo VPS Production Deployment Guide

This guide provides complete instructions for deploying your AI-OS system with Retool dashboard to your Contabo VPS.

## Overview

Your production deployment includes:
- **AI-OS API**: Lead processing and automation
- **n8n**: Workflow automation and webhooks
- **Retool**: Dashboard for monitoring and management
- **Caddy**: Reverse proxy with automatic SSL
- **PostgreSQL**: Database for n8n and Retool

## Prerequisites

1. **Contabo VPS** with Ubuntu/Debian
2. **Domain name** (optional, can use IP address)
3. **Local environment** configured and tested
4. **SSH access** to your VPS

## Quick Deployment

### 1. Deploy to Contabo VPS

```bash
# Run the automated deployment script
./deploy-to-contabo.sh
```

This script will:
1. Copy setup script to your VPS
2. Install Docker, Docker Compose, and Caddy
3. Sync your project files
4. Configure reverse proxy with SSL
5. Start all services
6. Test endpoints

### 2. Access Your Services

After deployment, access your services at:

**With Domain (SSL enabled):**
- **Retool Dashboard**: `https://yourdomain.com/retool`
- **n8n Interface**: `https://yourdomain.com/n8n`
- **AI-OS API**: `https://yourdomain.com/api/v1/health`
- **Webhooks**: `https://yourdomain.com/webhook/ghl/lead`

**With IP Address (HTTP only):**
- **Retool Dashboard**: `http://YOUR_VPS_IP/retool`
- **n8n Interface**: `http://YOUR_VPS_IP/n8n`
- **AI-OS API**: `http://YOUR_VPS_IP/api/v1/health`
- **Webhooks**: `http://YOUR_VPS_IP/webhook/ghl/lead`

## Manual Deployment Steps

If you prefer manual deployment or need to troubleshoot:

### 1. Prepare Your VPS

```bash
# SSH into your VPS
ssh root@YOUR_VPS_IP

# Run the setup script
curl -fsSL https://raw.githubusercontent.com/your-repo/AI-OS/main/contabo-setup.sh | bash
```

### 2. Upload Your Project

```bash
# From your local machine, sync the project
rsync -avz --progress \
    --exclude 'node_modules' \
    --exclude '.git' \
    --exclude 'venv' \
    --exclude '*.log' \
    . root@YOUR_VPS_IP:/opt/AI-OS/

# Copy environment file
scp .env root@YOUR_VPS_IP:/opt/AI-OS/
```

### 3. Update Environment for Production

```bash
# SSH into your VPS
ssh root@YOUR_VPS_IP
cd /opt/AI-OS

# Update environment variables
./scripts/update_production_env.sh yourdomain.com
```

### 4. Start Services

```bash
# Pull latest images and start services
docker-compose pull
docker-compose up -d

# Check service status
docker-compose ps
```

### 5. Configure Reverse Proxy

```bash
# Create Caddyfile (already done by deploy script)
# Start Caddy
caddy start --config Caddyfile
```

## Service Configuration

### Ports and Services

| Service | Internal Port | External Access |
|---------|---------------|-----------------|
| Retool | 3000 | `/retool/*` |
| n8n | 5678 | `/n8n/*`, `/webhook/*` |
| AI-OS API | 5002 | `/api/*` |
| PostgreSQL | 5432 | Internal only |

### Environment Variables

Key production environment variables:

```bash
# Domain configuration
RETOOL_HOSTNAME=https://yourdomain.com
WEBHOOK_URL=https://yourdomain.com

# Security (auto-generated in production)
RETOOL_JWT_SECRET=secure-random-string
RETOOL_ENCRYPTION_KEY=secure-random-string

# Database connections
PG_CONN_STRING_READONLY=postgres://user:pass@host:port/db
SUPABASE_URL=https://your-project.supabase.co
```

## Post-Deployment Setup

### 1. Configure Retool

1. Access Retool at `https://yourdomain.com/retool`
2. Create admin account on first visit
3. Generate API key in Settings → API
4. Update `.env` file with the API key:
   ```bash
   echo "RETOOL_API_KEY=your_generated_key" >> .env
   docker-compose restart retool
   ```

### 2. Import Dashboard

```bash
# SSH into your VPS
ssh root@YOUR_VPS_IP
cd /opt/AI-OS

# Run the import script
./scripts/retool_import.sh
```

### 3. Update GoHighLevel Webhooks

Update your GHL webhooks to use the production URL:
- **Webhook URL**: `https://yourdomain.com/webhook/ghl/lead`

### 4. Test the System

```bash
# Test all endpoints
curl -f https://yourdomain.com/api/v1/health
curl -f https://yourdomain.com/n8n/healthz
curl -f https://yourdomain.com/retool/api/checkHealth

# Test webhook
curl -X POST https://yourdomain.com/webhook/ghl/lead \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'
```

## Monitoring and Maintenance

### View Logs

```bash
# SSH into your VPS
ssh root@YOUR_VPS_IP
cd /opt/AI-OS

# View all service logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f retool
docker-compose logs -f n8n
docker-compose logs -f api

# View Caddy logs
tail -f /var/log/caddy/aios.log
```

### Service Management

```bash
# Restart all services
docker-compose restart

# Restart specific service
docker-compose restart retool

# Update services
docker-compose pull
docker-compose up -d

# Check service health
docker-compose ps
```

### Backup and Updates

```bash
# Backup data volumes
docker run --rm -v ai-os_postgres-data:/data -v $(pwd):/backup ubuntu tar czf /backup/postgres-backup.tar.gz /data
docker run --rm -v ai-os_retool-data:/data -v $(pwd):/backup ubuntu tar czf /backup/retool-backup.tar.gz /data

# Update the system
git pull origin main
docker-compose pull
docker-compose up -d
```

## Troubleshooting

### Common Issues

1. **Services not starting**
   ```bash
   # Check logs
   docker-compose logs
   
   # Restart services
   docker-compose down
   docker-compose up -d
   ```

2. **SSL certificate issues**
   ```bash
   # Check Caddy logs
   tail -f /var/log/caddy/aios.log
   
   # Restart Caddy
   caddy stop
   caddy start --config Caddyfile
   ```

3. **Database connection issues**
   ```bash
   # Check PostgreSQL
   docker-compose exec postgres psql -U n8n -d n8n -c "SELECT 1;"
   
   # Check Supabase connection
   psql "$PG_CONN_STRING_READONLY" -c "SELECT COUNT(*) FROM leads;"
   ```

### Performance Optimization

1. **Monitor resource usage**
   ```bash
   # Check system resources
   htop
   df -h
   docker stats
   ```

2. **Optimize Docker**
   ```bash
   # Clean up unused containers and images
   docker system prune -a
   
   # Limit log size in docker-compose.yml
   logging:
     driver: "json-file"
     options:
       max-size: "10m"
       max-file: "3"
   ```

## Security Considerations

1. **Firewall Configuration**
   - Only ports 80, 443, and SSH should be open to the internet
   - Internal services (5002, 5678, 3000) are proxied through Caddy

2. **SSL/TLS**
   - Caddy automatically manages SSL certificates
   - All traffic is encrypted in production

3. **Database Security**
   - Use read-only database user for Retool
   - PostgreSQL is not exposed externally

4. **Regular Updates**
   - Keep Docker images updated
   - Monitor security advisories for dependencies

## Support

For deployment issues:
1. Check the troubleshooting section above
2. Review service logs for error messages
3. Verify all environment variables are set correctly
4. Ensure your domain DNS is pointing to the VPS IP
