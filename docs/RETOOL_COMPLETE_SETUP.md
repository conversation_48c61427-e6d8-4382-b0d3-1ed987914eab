# Complete Retool Dashboard Setup Guide

This guide provides step-by-step instructions for setting up the Retool dashboard for your AI-OS system.

## Overview

The Retool dashboard provides:
- **KPI Monitoring**: Track active leads, offers sent, deals won, and API spend
- **Lead Management**: View and manage Tier-1 leads with detailed property information
- **Workflow Monitoring**: Monitor n8n workflow executions and agent errors
- **Configuration Management**: Update system configuration parameters
- **Real-time Updates**: Live data from your Supabase database

## Prerequisites

Before starting, ensure you have:

1. **Docker and Docker Compose** installed and running
2. **Supabase database** set up with all migrations applied
3. **Environment variables** configured in `.env` file:
   - `RETOOL_API_KEY` (will be generated during setup)
   - `RETOOL_ORG_ID`
   - `PG_CONN_STRING_READONLY`
   - `GHL_API_KEY`
   - `SUPABASE_URL`
   - `SUPABASE_KEY`

## Quick Setup (Automated)

Run the automated setup script:

```bash
./scripts/setup_retool.sh
```

This script will:
1. Check all prerequisites
2. Start the Retool Docker service
3. Wait for Retool to be ready
4. Guide you through first-time setup if needed
5. Import dashboard and resources automatically

## Manual Setup Steps

If you prefer manual setup or need to troubleshoot:

### 1. Start Retool Service

```bash
# Start all services including Retool
docker-compose up -d

# Or start just Retool
docker-compose up -d retool
```

### 2. Access Retool Web Interface

1. Open your browser and go to: `http://localhost:3000`
2. If this is the first time, you'll be prompted to create an admin account
3. Complete the signup process and log in

### 3. Generate API Key

1. In Retool, go to **Settings → API**
2. Click **Create API Key**
3. Copy the generated key
4. Add it to your `.env` file:
   ```bash
   RETOOL_API_KEY=your_generated_api_key_here
   ```

### 4. Import Resources and Dashboard

```bash
# Make sure the import script is executable
chmod +x scripts/retool_import.sh

# Run the import
./scripts/retool_import.sh
```

## Dashboard Components

### KPI Cards
- **Active Tier 1 Leads**: Number of active Tier-1 leads in the pipeline
- **Offers Sent Today**: Number of offers sent today
- **Deals Won MTD**: Deals closed this month
- **OpenAI Spend Today**: Daily API spend (placeholder for future implementation)

### Lead Management Table
- View all Tier-1 leads with property addresses
- Filter and sort leads by status, date, etc.
- Click on leads to view detailed information

### Lead Details Drawer
- **Comps Tab**: View comparable properties
- **MAO Tab**: Maximum Allowable Offer calculations
- **Offers Tab**: Track offers made and their status
- **Notes Tab**: Add and view lead notes

## Database Schema

The dashboard relies on these key tables:

### Core Tables
- `leads`: Lead information and status
- `properties`: Property details and addresses
- `deals`: Deal tracking and status
- `pipeline`: Lead progression through stages
- `agent_errors`: Error logging for troubleshooting
- `system_config`: Configurable system parameters

### Key Views
- `vw_kpi_summary`: Aggregated KPIs for dashboard cards

## Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# Retool Configuration
RETOOL_API_KEY=your_api_key_here
RETOOL_ORG_ID=your_org_id
RETOOL_HOSTNAME=http://localhost:3000
RETOOL_JWT_SECRET=change-me-in-production
RETOOL_ENCRYPTION_KEY=change-me-in-production

# Database Connection
PG_CONN_STRING_READONLY=postgres://user:pass@host:port/db

# API Keys
GHL_API_KEY=your_ghl_api_key
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
```

### Resource Connections

The dashboard uses these resources:

1. **Supabase PostgreSQL**: Main database connection
   - Host: Your Supabase database host
   - Port: 5432 (or 6543 for pooler)
   - Database: postgres
   - SSL: Enabled

2. **GoHighLevel REST API**: CRM integration
   - Base URL: https://api.gohighlevel.com/v1
   - Authorization: Bearer token

## Troubleshooting

### Common Issues

1. **Retool won't start**
   ```bash
   # Check Docker logs
   docker-compose logs retool
   
   # Restart the service
   docker-compose restart retool
   ```

2. **Database connection fails**
   - Verify `PG_CONN_STRING_READONLY` is correct
   - Check Supabase database is accessible
   - Ensure SSL is enabled for Supabase connections

3. **Dashboard import fails**
   - Verify `RETOOL_API_KEY` is set correctly
   - Check Retool is fully started (wait 2-3 minutes)
   - Run import script again: `./scripts/retool_import.sh`

4. **No data showing**
   - Verify Supabase migrations are applied
   - Check database has sample data
   - Test database connection in Retool resources

### Logs and Debugging

```bash
# View Retool logs
docker-compose logs -f retool

# Check all services
docker-compose ps

# Test database connection
psql "$PG_CONN_STRING_READONLY" -c "SELECT COUNT(*) FROM leads;"
```

## Security Considerations

1. **Change default secrets** in production:
   - `RETOOL_JWT_SECRET`
   - `RETOOL_ENCRYPTION_KEY`

2. **Use read-only database user** for dashboard queries

3. **Restrict access** by setting `RETOOL_RESTRICTED_DOMAIN`

4. **Enable HTTPS** in production deployments

## Next Steps

After setup is complete:

1. **Test the dashboard** by creating sample leads
2. **Configure alerts** for critical metrics
3. **Set up automated backups** for Retool data
4. **Train team members** on dashboard usage
5. **Monitor performance** and optimize queries as needed

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review Docker logs for error messages
3. Verify all environment variables are set correctly
4. Ensure database migrations are applied
