# AI-OS Knowledge Pipeline Setup Guide

## 🧠 **Overview**

The Knowledge Pipeline enables your AI-OS to continuously learn and improve by ingesting documents, extracting structured knowledge, and applying lessons learned from each deal interaction.

## 🎯 **Key Features**

### Document Ingestion & Processing
- **Upload any document type**: PDF, TXT, MD, CSV, DOCX
- **Automatic content extraction**: Text parsing and chunking
- **Structured knowledge extraction**: Deal analysis templates, MAO calculations, negotiation strategies
- **Contextual summarization**: AI-generated summaries for better retrieval

### Agent Learning System
- **Performance tracking**: Monitor agent success rates and actions
- **Feedback loops**: Learn from successful and failed interactions
- **Knowledge effectiveness**: Track which knowledge helps most
- **Personalized recommendations**: Suggest improvements for each agent

### Retool Knowledge Management Interface
- **Document upload dashboard**: Easy file upload with categorization
- **Knowledge base browser**: View and manage all documents
- **Analytics dashboard**: Track knowledge usage and effectiveness
- **Template management**: Create and manage reusable templates

## 🚀 **Quick Setup**

### 1. Apply Database Migration

```bash
# Apply the knowledge management schema
cd supabase
supabase db push

# Or manually apply the migration
psql "$PG_CONN_STRING_READONLY" -f migrations/004_knowledge_management_enhancement_20250131.sql
```

### 2. Start the System

```bash
# Start all services
docker-compose up -d

# Verify knowledge pipeline is working
curl http://localhost:5002/api/v1/documents/list
```

### 3. Access Knowledge Management Dashboard

1. Go to your Retool instance: `http://localhost:3000/retool`
2. The Knowledge Management dashboard should be available
3. If not imported, run: `./scripts/retool_import.sh`

## 📚 **Using the Knowledge Pipeline**

### Document Upload Process

1. **Access the Dashboard**
   - Open Retool Knowledge Management interface
   - Navigate to the "Document Upload" section

2. **Select Document Type**
   - **Deal Analysis Template**: Complete deal evaluation processes
   - **MAO Calculation Method**: Mathematical formulas and calculations
   - **Market Analysis Guide**: Market evaluation criteria
   - **Negotiation Strategy**: Tactics and objection handling
   - **Contract Template**: Legal documents and clauses
   - **Follow-up Sequence**: Communication workflows
   - **General Knowledge**: Any other relevant information

3. **Set Priority Level**
   - **🔴 Critical**: Essential knowledge (MAO formulas, core strategies)
   - **🟡 High**: Important knowledge (negotiation tactics, market analysis)
   - **🟢 Medium**: Useful knowledge (templates, examples)
   - **⚪ Low**: Reference material (general information)

4. **Add Tags and Description**
   - Tags help with searchability (e.g., "wholesaling, mao, calculations")
   - Description explains what the AI should learn from this document

### Sample Documents to Upload

I've created sample templates for you to start with:

1. **MAO Calculation Template** (`knowledge_pipeline/sample_templates/mao_calculation_template.md`)
   - Complete step-by-step MAO calculation process
   - Real examples with numbers
   - Decision criteria and red flags

2. **Negotiation Strategy Template** (`knowledge_pipeline/sample_templates/negotiation_strategy_template.md`)
   - Tactics for different seller types
   - Objection handling scripts
   - Advanced negotiation techniques

### Upload These Templates

```bash
# Upload the sample templates through Retool interface:
# 1. MAO Calculation Template (Priority: Critical, Type: MAO Calculation Method)
# 2. Negotiation Strategy Template (Priority: High, Type: Negotiation Strategy)
```

## 🤖 **Agent Learning Integration**

### How Agents Learn

1. **Knowledge Application**: When agents process leads, they reference uploaded knowledge
2. **Action Tracking**: Every agent action is recorded with outcomes
3. **Feedback Collection**: Success/failure data is captured automatically
4. **Performance Analysis**: System identifies patterns and improvement areas
5. **Recommendation Generation**: Personalized suggestions for each agent

### Learning Feedback Loop

```
Lead Processing → Agent Actions → Outcome Recording → Knowledge Analysis → Improved Performance
```

### Example Learning Scenarios

**Scenario 1: MAO Calculation Improvement**
- Agent calculates MAO incorrectly
- Deal fails due to poor pricing
- System records failure and analyzes knowledge used
- Recommends additional MAO training materials
- Agent performance improves on next deals

**Scenario 2: Negotiation Success Pattern**
- Agent successfully negotiates using specific tactics
- System records success and knowledge applied
- Pattern is identified and reinforced
- Successful tactics are recommended to other agents

## 📊 **Knowledge Analytics**

### Performance Metrics

Track these KPIs in your Retool dashboard:

1. **Document Usage**
   - Most referenced documents
   - Knowledge effectiveness scores
   - Usage frequency by agent

2. **Agent Performance**
   - Success rates by agent
   - Improvement trends over time
   - Knowledge application effectiveness

3. **Learning Insights**
   - Best performing knowledge types
   - Areas needing improvement
   - Recommendation success rates

### Analytics Views Available

- **Document Analytics**: Usage and effectiveness by document
- **Agent Learning Insights**: Performance trends and feedback
- **Knowledge Effectiveness**: Which knowledge helps most

## 🔧 **Advanced Configuration**

### Custom Document Types

Add new document types by updating the Retool interface:

```javascript
// In Retool, update the documentTypeSelect options:
{
  "value": "your_custom_type",
  "label": "Your Custom Type"
}
```

### Knowledge Extraction Customization

Modify the deal analysis processor for your specific needs:

```python
# In knowledge_pipeline/deal_analysis_processor.py
# Add custom extraction patterns for your documents
```

### Agent Learning Rules

Customize learning rules in the agent learning system:

```python
# In knowledge_pipeline/agent_learning_system.py
# Modify success criteria and recommendation logic
```

## 🎯 **Best Practices**

### Document Organization

1. **Use Consistent Naming**: Clear, descriptive filenames
2. **Proper Categorization**: Choose correct document types
3. **Meaningful Tags**: Use searchable, relevant tags
4. **Clear Descriptions**: Explain what the AI should learn

### Knowledge Quality

1. **Specific Examples**: Include real numbers and scenarios
2. **Step-by-Step Processes**: Break down complex procedures
3. **Decision Criteria**: Clear rules for when to apply knowledge
4. **Regular Updates**: Keep knowledge current with market changes

### Continuous Improvement

1. **Monitor Analytics**: Review knowledge effectiveness regularly
2. **Update Documents**: Refine based on performance data
3. **Add New Knowledge**: Upload new learnings from successful deals
4. **Remove Outdated Info**: Delete or update obsolete knowledge

## 🚨 **Troubleshooting**

### Common Issues

1. **Document Upload Fails**
   ```bash
   # Check API endpoint
   curl http://localhost:5002/api/v1/documents/list
   
   # Check file permissions
   ls -la knowledge_pipeline/sample_templates/
   ```

2. **Knowledge Not Being Applied**
   ```bash
   # Check knowledge base content
   psql "$PG_CONN_STRING_READONLY" -c "SELECT COUNT(*) FROM knowledge_base;"
   
   # Verify embeddings are generated
   psql "$PG_CONN_STRING_READONLY" -c "SELECT COUNT(*) FROM knowledge_base WHERE embedding IS NOT NULL;"
   ```

3. **Learning System Not Recording**
   ```bash
   # Check agent learning feedback table
   psql "$PG_CONN_STRING_READONLY" -c "SELECT COUNT(*) FROM agent_learning_feedback;"
   ```

### Debug Mode

Enable debug logging for knowledge pipeline:

```bash
# Set environment variable
export KNOWLEDGE_PIPELINE_DEBUG=true

# Restart services
docker-compose restart api
```

## 📈 **Success Metrics**

Track these metrics to measure knowledge pipeline success:

### Knowledge Metrics
- **Documents uploaded**: Target 20+ high-quality documents
- **Knowledge usage rate**: Target 80%+ of agent actions use knowledge
- **Knowledge effectiveness**: Target 0.8+ average effectiveness score

### Agent Learning Metrics
- **Performance improvement**: Target 20%+ improvement in success rates
- **Learning velocity**: Target 90%+ of feedback incorporated
- **Knowledge application**: Target 95%+ of relevant knowledge used

### Business Impact
- **Deal success rate**: Improved conversion rates
- **Average profit per deal**: Higher profits through better analysis
- **Time to close**: Faster deal processing

## 🎉 **Next Steps**

1. **Upload Your First Documents**
   - Start with the provided MAO and negotiation templates
   - Add your own deal analysis methods

2. **Monitor Agent Performance**
   - Review learning analytics weekly
   - Identify improvement opportunities

3. **Expand Knowledge Base**
   - Add market-specific knowledge
   - Include contract templates
   - Document successful deal strategies

4. **Optimize Continuously**
   - Refine based on performance data
   - Update knowledge as market changes
   - Train agents on new knowledge

Your AI-OS will now continuously learn and improve with every deal, becoming smarter and more effective over time! 🚀
