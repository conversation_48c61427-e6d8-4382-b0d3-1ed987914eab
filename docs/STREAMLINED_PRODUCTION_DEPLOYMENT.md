# Streamlined AI-OS Production Deployment

## 🎯 **Simplified Architecture**

Your streamlined AI-OS system includes only the essential components:

- **AI-OS API**: Complete automation pipeline with integrated webhooks
- **Retool Dashboard**: Monitoring and management interface  
- **PostgreSQL**: Database for Retool
- **Caddy**: Reverse proxy with automatic SSL

**Removed:** n8n (workflow automation now handled directly by AI-OS API)

## 🚀 **Quick Deployment to Contabo VPS**

### 1. Deploy with One Command

```bash
./deploy-to-contabo.sh
```

**What this does:**
1. Sets up your Contabo VPS with <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and firewall
2. Syncs your project files
3. Configures SSL and reverse proxy
4. Starts AI-OS API and Retool services
5. Tests all endpoints

### 2. Access Your System

**With Domain (SSL enabled):**
- **Retool Dashboard**: `https://yourdomain.com/retool`
- **AI-OS API**: `https://yourdomain.com/api/v1/health`
- **Webhooks**: `https://yourdomain.com/webhook/ghl/lead`

**With IP Address (HTTP only):**
- **Retool Dashboard**: `http://YOUR_VPS_IP/retool`
- **AI-OS API**: `http://YOUR_VPS_IP/api/v1/health`
- **Webhooks**: `http://YOUR_VPS_IP/webhook/ghl/lead`

## 📊 **Service Architecture**

```
Internet → Caddy (SSL/Proxy) → AI-OS API (Port 5002)
                             → Retool (Port 3000)
                             → PostgreSQL (Internal)
```

### Port Configuration

| Service | Internal Port | External Access | Purpose |
|---------|---------------|-----------------|---------|
| AI-OS API | 5002 | `/api/*`, `/webhook/*` | Automation + Webhooks |
| Retool | 3000 | `/retool/*`, `/` | Dashboard |
| PostgreSQL | 5432 | Internal only | Retool database |

## 🔧 **Key Features**

### Direct API Integration
- **GoHighLevel webhooks** → AI-OS API directly
- **Master Deal Orchestrator** handles complete pipeline
- **No external workflow engine** needed

### Automated Deal Pipeline
1. **Webhook Reception**: `POST /webhook/ghl/lead`
2. **Lead Classification**: Tier 1/2 automatic detection
3. **Property Analysis**: Multi-tier comping system
4. **MAO Calculation**: Automated offer generation
5. **Deal Tracking**: Status updates in Retool

### Monitoring & Management
- **Retool Dashboard**: Real-time KPIs and lead management
- **API Health Checks**: `/api/v1/health`
- **Comprehensive Logging**: Docker logs + Caddy logs

## 🛠 **Post-Deployment Setup**

### 1. Configure Retool Dashboard

```bash
# SSH into your VPS
ssh root@YOUR_VPS_IP
cd /opt/AI-OS

# Access Retool and create admin account
# Go to: https://yourdomain.com/retool

# After creating account, generate API key and update environment
echo "RETOOL_API_KEY=your_generated_key" >> .env
docker-compose restart retool

# Import dashboard
./scripts/retool_import.sh
```

### 2. Update GoHighLevel Webhooks

Update your GHL webhook URL to:
```
https://yourdomain.com/webhook/ghl/lead
```

### 3. Test the System

```bash
# Test API health
curl https://yourdomain.com/api/v1/health

# Test webhook endpoint
curl -X POST https://yourdomain.com/webhook/ghl/lead \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'

# Test Retool
curl https://yourdomain.com/retool/api/checkHealth
```

## 📈 **Benefits of Streamlined Architecture**

### Performance
- **50% fewer resources** - No n8n container
- **Faster response times** - Direct API processing
- **Simpler networking** - Fewer internal connections

### Reliability
- **Fewer failure points** - Less complex architecture
- **Direct control** - All logic in one codebase
- **Easier debugging** - Single service logs

### Maintenance
- **Simpler updates** - One main service to update
- **Easier monitoring** - Fewer services to watch
- **Lower complexity** - Less configuration needed

## 🔍 **Monitoring & Logs**

### Service Status
```bash
# Check all services
docker-compose ps

# View API logs
docker-compose logs -f api

# View Retool logs
docker-compose logs -f retool

# View Caddy logs
tail -f /var/log/caddy/aios.log
```

### Health Checks
```bash
# API health
curl https://yourdomain.com/api/v1/health

# Retool health
curl https://yourdomain.com/retool/api/checkHealth

# Database connection test
docker-compose exec postgres psql -U retool -d retool -c "SELECT 1;"
```

## 🚨 **Troubleshooting**

### Common Issues

1. **API not responding**
   ```bash
   docker-compose logs api
   docker-compose restart api
   ```

2. **Retool not accessible**
   ```bash
   docker-compose logs retool
   docker-compose restart retool
   ```

3. **SSL certificate issues**
   ```bash
   tail -f /var/log/caddy/aios.log
   caddy reload --config Caddyfile
   ```

### Performance Optimization

```bash
# Monitor resource usage
docker stats

# Clean up unused resources
docker system prune

# Check disk space
df -h
```

## 🔐 **Security**

### Firewall Configuration
- **Port 80/443**: Web traffic (Caddy)
- **Port 22**: SSH access
- **Internal ports**: Not exposed externally

### SSL/TLS
- **Automatic certificates**: Managed by Caddy
- **HTTPS redirect**: All traffic encrypted
- **Secure headers**: Added by Caddy

### Database Security
- **Internal access only**: PostgreSQL not exposed
- **Dedicated user**: Retool-specific database user
- **Connection encryption**: SSL enabled

## 📋 **Maintenance Tasks**

### Regular Updates
```bash
# Update system packages
apt update && apt upgrade -y

# Update Docker images
docker-compose pull
docker-compose up -d

# Backup data
docker run --rm -v ai-os_postgres-data:/data -v $(pwd):/backup ubuntu tar czf /backup/postgres-backup.tar.gz /data
```

### Monitoring
- **Daily**: Check service status and logs
- **Weekly**: Review Retool dashboard metrics
- **Monthly**: Update system and Docker images

## 🎉 **Success Metrics**

With this streamlined deployment, you achieve:

- ✅ **Automated Deal Pipeline**: Lead to offer in minutes
- ✅ **Real-time Monitoring**: Retool dashboard with live KPIs
- ✅ **Production Ready**: SSL, monitoring, and logging
- ✅ **Scalable Architecture**: Easy to expand when needed
- ✅ **Cost Effective**: Minimal resource usage
- ✅ **Easy Maintenance**: Simple, focused architecture

Your automated deal closing machine is now **production-ready** and optimized for performance!
