{"lead_ingestion": {"enabled": true, "webhook_path": "/webhook/lead", "timeout_seconds": 60, "tiers": {"tier1": {"auto_process": true, "workflows": ["comping", "mao_calculation", "exit_strategy"]}, "tier2": {"auto_process": false, "workflows": []}}, "retry": {"max_attempts": 3, "backoff_seconds": 5}}, "comping": {"enabled": true, "sources": {"rentspree_api": {"priority": 1, "enabled": true, "timeout_seconds": 15, "max_retries": 2, "weight": 0.9, "api_endpoint": "https://api.rentspree.com/v1/properties/search", "requires_auth": true}, "realty_mole_api": {"priority": 2, "enabled": true, "timeout_seconds": 15, "max_retries": 2, "weight": 0.8, "api_endpoint": "https://api.realtymole.com/v1/properties", "requires_auth": true}, "smarty_streets_api": {"priority": 3, "enabled": true, "timeout_seconds": 10, "max_retries": 2, "weight": 0.7, "api_endpoint": "https://us-street.api.smartystreets.com/street-address", "requires_auth": true}, "batchleads_scraper": {"priority": 4, "enabled": true, "timeout_seconds": 45, "max_retries": 2, "weight": 0.6, "scraping_enabled": true, "session_management": true}, "privy_scraper": {"priority": 5, "enabled": false, "timeout_seconds": 45, "max_retries": 2, "weight": 0.5, "scraping_enabled": true, "session_management": true}, "lotside_scraper": {"priority": 6, "enabled": false, "timeout_seconds": 45, "max_retries": 2, "weight": 0.4, "scraping_enabled": true, "session_management": true}}, "business_rules": {"max_distance_miles": 1.0, "max_age_days": 180, "max_sqft_diff_percent": 20, "min_comps_required": 3, "preferred_comps_count": 5, "max_comps_to_use": 10, "adjust_for_age": true, "adjust_for_condition": true, "adjust_for_size": true, "size_adjustment_rate": 0.1, "age_adjustment_rate": 0.005}, "parallel_execution": true}, "mao_calculation": {"enabled": true, "parameters": {"profit_margin": 0.2, "holding_cost_percent": 0.02, "closing_cost_percent": 0.03, "contingency_percent": 0.05}, "repair_cost_factors": {"poor": 40, "fair": 25, "good": 15, "excellent": 5}, "repair_cost_adjustments": {"age_multiplier": {"pre_1950": 1.3, "1950_1980": 1.2, "1980_2000": 1.1, "post_2000": 1.0}, "special_features": {"pool": 5000, "large_lot": 2000, "multiple_stories": 3000}}, "notification_on_completion": true}, "exit_strategy": {"enabled": true, "strategies": {"wholesale": {"min_arv": 100000, "min_equity_percent": 0.25, "max_repair_percent": 0.15, "priority": 3}, "fix_and_flip": {"min_arv": 200000, "min_profit": 30000, "max_repair_percent": 0.25, "min_roi": 0.15, "priority": 2}, "buy_and_hold": {"min_cash_flow": 300, "min_cap_rate": 0.06, "max_repair_percent": 0.2, "priority": 1}, "subject_to": {"min_equity": 20000, "max_ltv": 0.8, "priority": 4}}, "buyer_matching": {"enabled": true, "max_matches": 10, "match_factors": {"location_weight": 0.3, "price_range_weight": 0.2, "strategy_weight": 0.3, "property_type_weight": 0.1, "condition_weight": 0.1}, "score_thresholds": {"high_match": 0.8, "medium_match": 0.6, "low_match": 0.4}}}, "follow_up": {"enabled": true, "schedule": {"initial": {"days": 2, "priority": "high"}, "reminder": {"days": 5, "priority": "medium"}, "final": {"days": 10, "priority": "low"}}, "max_follow_ups": 5, "time_window": {"start_hour": 9, "end_hour": 18, "timezone": "America/New_York", "weekdays_only": true}, "randomize_timing": true, "randomize_templates": true}, "disposition": {"enabled": true, "schedule": {"initial_offer": {"days": 0, "priority": "high"}, "first_follow_up": {"days": 3, "priority": "medium"}, "second_follow_up": {"days": 7, "priority": "medium"}, "final_follow_up": {"days": 14, "priority": "low"}}, "time_window": {"start_hour": 9, "end_hour": 18, "timezone": "America/New_York", "weekdays_only": true}, "randomize_timing": true, "randomize_templates": true}, "approval_workflow": {"enabled": true, "default_timeout_seconds": 86400, "notification_types": {"approval_needed": "sms", "approved": "sms", "rejected": "sms", "timed_out": "sms"}, "critical_approvals": ["offer_submission", "price_adjustment", "contract_signing"]}, "agent_communication": {"enabled": true, "hot_lead_processing": {"immediate_email": true, "immediate_sms": true, "notify_eric": true, "priority": "critical", "auto_comping": true, "auto_mao_calculation": true, "auto_offer_generation": true}, "tier_2_follow_up": {"frequency_days": 14, "communication_types": ["email", "sms"], "max_attempts": 10, "priority": "medium", "auto_nurture": true, "re_evaluation_interval_days": 30}, "tier_3_dnc": {"stop_all_communication": true, "add_to_dnc_list": true, "notify_team": false}, "templates": {"hot_lead_email": "hot_lead_immediate_interest_email", "hot_lead_sms": "hot_lead_immediate_sms", "tier2_email": "tier2_biweekly_checkin_email", "tier2_sms": "tier2_biweekly_checkin_sms", "offer_presentation_email": "property_offer_presentation", "follow_up_with_offer": "follow_up_offer_reminder"}, "eric_notifications": {"enabled": true, "contact_id": "ERIC_CONTACT_ID_PLACEHOLDER", "hot_lead_alerts": true, "call_requests": true, "agent_responses": true, "offer_notifications": true, "deal_status_updates": true}}, "automated_deal_pipeline": {"enabled": true, "tier_1_automation": {"auto_comping": true, "auto_mao_calculation": true, "auto_offer_generation": true, "auto_offer_delivery": true, "auto_follow_up_scheduling": true, "max_processing_time_minutes": 15}, "offer_management": {"auto_generate_offers": true, "offer_delivery_methods": ["email", "sms"], "follow_up_schedule": [1, 3, 7, 14, 30], "auto_adjust_offers": false, "escalation_after_days": 7}, "deal_tracking": {"track_offer_responses": true, "auto_update_lead_status": true, "notify_on_acceptance": true, "notify_on_counter_offer": true, "auto_schedule_closing": false}}, "hot_lead_processing": {"enabled": true, "immediate_actions": ["send_email", "send_sms", "notify_eric", "trigger_comping", "calculate_mao"], "timeline": {"initial_contact": "immediate", "follow_up": "2_hours", "offer_generation": "24_hours"}, "escalation": {"no_response_hours": 4, "escalate_to_eric": true, "request_phone_call": true}}}