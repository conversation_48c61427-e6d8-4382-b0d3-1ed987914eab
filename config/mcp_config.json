{"mcp_servers": [{"name": "rag_knowledge_base", "type": "CrawlForAI_RAG_MCP", "url_env_var": "RAG_MCP_URL", "enabled": true, "default_params": {"top_k": 5}}, {"name": "supabase_database_operations", "type": "SupabaseMCP", "url_env_var": "SUPABASE_MCP_URL", "token_env_var": "SUPABASE_MCP_TOKEN", "enabled": true}, {"name": "web_search", "type": "BraveSearchMCP", "url_env_var": "BRAVE_MCP_URL", "api_key_env_var": "BRAVE_API_KEY", "enabled": true, "default_params": {"country": "US"}}, {"name": "gohighlevel_comprehensive", "type": "GoHighLevel_MCP", "command": "node", "args": ["./ghl-mcp-server/dist/server.js"], "env_vars": {"GHL_API_KEY": "GHL_API_KEY", "GHL_BASE_URL": "https://rest.gohighlevel.com/v1", "GHL_LOCATION_ID": "GHL_LOCATION_ID", "NODE_ENV": "production"}, "enabled": true, "description": "Comprehensive GoHighLevel MCP with 269+ tools for complete CRM automation", "categories": ["contact_management", "messaging_conversations", "blog_management", "opportunity_management", "calendar_appointments", "email_marketing", "location_management", "social_media", "custom_objects", "workflows", "payments", "invoices_billing"]}], "ghl_integration": {"webhook_logging_table": "ghl_zapier_webhook_logs", "aios_trigger_base_url_env_var": "AIOS_INTERNAL_WEBHOOK_BASE_URL", "aios_trigger_api_key_env_var": "AIOS_INTERNAL_API_KEY"}}