
# AI-OS Automated Deal Closing System

A streamlined, production-ready system for automated real estate deal closing with integrated monitoring and management.

## 🎯 **System Overview**

Your AI-OS system provides complete automation from lead to offer:

- **AI-OS API**: Integrated automation pipeline with webhook processing
- **Retool Dashboard**: Real-time monitoring and lead management
- **Master Deal Orchestrator**: Complete deal pipeline automation
- **GoHighLevel Integration**: Direct MCP integration for CRM management
- **Multi-tier Property Analysis**: RentCast, Realie.ai, <PERSON>, BatchData MCP

## 🚀 **Quick Start**

### Production Deployment (Contabo VPS)

```bash
# Deploy to your Contabo VPS
./deploy-to-contabo.sh
```

### Local Development

```bash
# Start services locally
docker-compose up -d

# Test the deployment
./scripts/test_streamlined_deployment.sh
```

## 📊 **Dashboard Features**

- **KPI Monitoring**: Track active leads, offers sent, deals won, and API spend
- **Lead Management**: View and manage Tier-1 leads with property details
- **Deal Pipeline**: Monitor lead progression through stages
- **Error Logging**: Monitor agent errors and troubleshoot issues
- **Configuration Management**: Override system configuration parameters

## 🏗 **Architecture**

```
GHL Webhook → AI-OS API → Master Deal Orchestrator → Agents → Retool Dashboard
                ↓
            Supabase Database
```

**Streamlined Components:**
- **AI-OS API** (Port 5002): Complete automation + webhook handling
- **Retool Dashboard** (Port 3000): Monitoring and management
- **PostgreSQL**: Database for Retool
- **Caddy**: Reverse proxy with SSL

## Prerequisites

Before deployment, ensure you have:

1. A Retool account with admin access
2. Access to your Supabase PostgreSQL database
3. GoHighLevel API credentials
4. Proper environment variables configured

## Environment Variables Setup

1. Copy the `.env.example` file to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file and replace the placeholder values with your actual credentials:
   ```bash
   # Retool credentials
   RETOOL_API_KEY=your_retool_api_key_here
   RETOOL_ORG_ID=your_retool_org_id_here

   # Database credentials
   PG_CONN_STRING_READONLY=postgres://username:password@hostname:port/database

   # API credentials
   GHL_API_KEY=your_ghl_api_key_here
   ```

3. Ensure proper file permissions:
   ```bash
   chmod 600 .env
   ```

## Dashboard Import Instructions

### Option 1: Automatic Import (Recommended)

1. Run the provided `_apply.sh` script:
   ```bash
   ./scripts/setup_dashboard.sh
   ```

2. The script will:
   - Validate your environment variables
   - Generate the dashboard JSON with your credentials
   - Import the dashboard into your Retool instance
   - Provide a URL to access your dashboard

### Option 2: Manual Import

1. Generate the dashboard JSON:
   ```bash
   ./scripts/generate_dashboard.sh
   ```

2. Log in to your Retool account

3. Navigate to "Apps" and click "Create New"

4. Select "Import from JSON"

5. Copy and paste the contents of the generated `retool_dashboard_with_env.json` file

6. Click "Import"

7. Configure the resources:
   - For the Supabase resource, use your PostgreSQL connection string
   - For the GoHighLevel resource, use your API key

## Resource Configuration

### Supabase PostgreSQL Resource

1. In Retool, go to "Resources" and click "Create New"
2. Select "PostgreSQL"
3. Enter a name (e.g., "Supabase")
4. For the connection string, use: `{{process.env.PG_CONN_STRING_READONLY}}`
5. Click "Create Resource"

### GoHighLevel REST Resource

1. In Retool, go to "Resources" and click "Create New"
2. Select "REST API"
3. Enter a name (e.g., "REST_API_GHL")
4. For the base URL, use: `https://api.gohighlevel.com/v1`
5. Add a header with key "Authorization" and value: `Bearer {{process.env.GHL_API_KEY}}`
6. Click "Create Resource"

## Security Best Practices

1. **Environment Variables**: Always use environment variables for sensitive credentials. Never hardcode them in your dashboard JSON.
2. **Read-Only Database Access**: Use a read-only database user for the dashboard to prevent accidental data modification.
3. **API Key Rotation**: Regularly rotate your API keys and update them in your environment variables.
4. **Access Control**: Limit dashboard access to authorized personnel only.
5. **Audit Logging**: Enable audit logging in Retool to track who is accessing and modifying the dashboard.
6. **Secure Storage**: Store your `.env` file securely and never commit it to version control.

## Troubleshooting

### Dashboard Import Fails

- Verify that your Retool API key has the necessary permissions
- Check that your Retool organization ID is correct
- Ensure the dashboard JSON is properly formatted

### Database Connection Issues

- Verify that your PostgreSQL connection string is correct
- Ensure your database user has the necessary permissions
- Check that your database is accessible from Retool

### API Connection Issues

- Verify that your GoHighLevel API key is valid
- Check that the API endpoints are accessible from Retool
- Ensure you're using the correct API version

### KPI Data Not Showing

- Verify that the `vw_kpi_summary` view exists in your database
- Check that the view has the expected columns
- Ensure your database user has permission to access the view

### File Upload Issues

- Verify that the ingest endpoint is correctly configured
- Check that your GoHighLevel API key has the necessary permissions
- Ensure the file format is supported

## Customization

The dashboard is designed to be customizable to meet your specific needs:

1. **KPI Thresholds**: Adjust the conditional formatting thresholds in the KPI tiles to match your business targets.
2. **Table Columns**: Modify the columns displayed in the tables to show the most relevant information.
3. **Auto-Refresh Interval**: Change the auto-refresh interval to balance real-time updates with performance.
4. **Export Options**: Customize the export functionality to include additional data or different formats.
5. **Notifications**: Adjust the notification settings to match your alerting preferences.

## Maintenance

To keep your dashboard running smoothly:

1. **Regular Updates**: Periodically check for updates to the dashboard JSON and import them.
2. **Database Optimization**: Ensure your database views are optimized for performance.
3. **Credential Rotation**: Regularly rotate your API keys and database credentials.
4. **Error Monitoring**: Regularly check the error logs to identify and resolve issues.
5. **User Feedback**: Collect feedback from users to identify areas for improvement.

## Support

If you encounter any issues with the dashboard, please:

1. Check the troubleshooting section above
2. Review the Retool documentation at https://docs.retool.com/
3. Contact your system administrator for assistance

## License

This dashboard is part of the AI-OS project and is licensed under the same terms as the main project.