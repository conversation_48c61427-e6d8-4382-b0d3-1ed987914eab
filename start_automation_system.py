#!/usr/bin/env python3
"""
Start Automation System - Launch the Complete Automated Deal Closing Machine
This script starts all components needed for the automated real estate wholesaling system
"""

import asyncio
import logging
import os
import sys
import subprocess
import time
from pathlib import Path

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/system_startup.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def check_prerequisites():
    """Check if all prerequisites are met"""
    logger.info("🔍 Checking system prerequisites...")
    
    # Check if required directories exist
    required_dirs = ['logs', 'memory_store', 'data']
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            logger.info(f"Creating directory: {dir_name}")
            os.makedirs(dir_name, exist_ok=True)
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        logger.warning("⚠️  .env file not found. Please create it with required API keys.")
        return False
    
    # Check if GoHighLevel MCP server is running
    try:
        import requests
        response = requests.get('http://localhost:3000/health', timeout=5)
        if response.status_code == 200:
            logger.info("✅ GoHighLevel MCP server is running")
        else:
            logger.warning("⚠️  GoHighLevel MCP server health check failed")
    except Exception as e:
        logger.warning(f"⚠️  Could not connect to GoHighLevel MCP server: {e}")
        logger.info("Starting GoHighLevel MCP server...")
        start_ghl_mcp_server()
    
    logger.info("✅ Prerequisites check completed")
    return True

def start_ghl_mcp_server():
    """Start the GoHighLevel MCP server"""
    try:
        logger.info("🚀 Starting GoHighLevel MCP server...")
        
        # Change to the GHL MCP server directory
        ghl_dir = os.path.join(os.getcwd(), 'ghl-mcp-server')
        
        if not os.path.exists(ghl_dir):
            logger.error("❌ GoHighLevel MCP server directory not found")
            return False
        
        # Start the server in the background
        process = subprocess.Popen(
            ['npm', 'start'],
            cwd=ghl_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait a moment for the server to start
        time.sleep(5)
        
        # Check if it's running
        if process.poll() is None:
            logger.info("✅ GoHighLevel MCP server started successfully")
            return True
        else:
            logger.error("❌ Failed to start GoHighLevel MCP server")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error starting GoHighLevel MCP server: {e}")
        return False

def display_startup_banner():
    """Display the startup banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════════════════════╗
    ║                                                                              ║
    ║                    🤖 AUTOMATED DEAL CLOSING MACHINE 🤖                     ║
    ║                                                                              ║
    ║                        Real Estate Wholesaling Automation                   ║
    ║                                                                              ║
    ║  🎯 Lead Monitoring & Processing                                             ║
    ║  📊 Property Analysis & Comping                                              ║
    ║  💰 MAO Calculation & Offer Generation                                       ║
    ║  📧 Automated Communication & Follow-ups                                     ║
    ║  🔔 Real-time Notifications & Alerts                                         ║
    ║                                                                              ║
    ║                           SYSTEM STARTING...                                ║
    ║                                                                              ║
    ╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def display_system_status():
    """Display the current system status"""
    status = """
    ╔══════════════════════════════════════════════════════════════════════════════╗
    ║                              SYSTEM STATUS                                   ║
    ╠══════════════════════════════════════════════════════════════════════════════╣
    ║                                                                              ║
    ║  🟢 GoHighLevel MCP Integration    - ACTIVE                                  ║
    ║  🟢 Lead Monitoring & Processing   - ACTIVE                                  ║
    ║  🟢 Property Analysis Pipeline     - ACTIVE                                  ║
    ║  🟢 Offer Generation System        - ACTIVE                                  ║
    ║  🟢 Follow-up Automation           - ACTIVE                                  ║
    ║  🟢 API Server & Webhooks          - ACTIVE                                  ║
    ║  🟢 Health Monitoring              - ACTIVE                                  ║
    ║                                                                              ║
    ║                        🚀 ALL SYSTEMS OPERATIONAL 🚀                        ║
    ║                                                                              ║
    ║  Your automated deal closing machine is now running and ready to:           ║
    ║                                                                              ║
    ║  • Monitor GoHighLevel for new Tier 1 leads                                 ║
    ║  • Automatically analyze properties and calculate offers                     ║
    ║  • Send offers and manage follow-up sequences                               ║
    ║  • Notify you of hot leads and deal opportunities                           ║
    ║  • Track and manage your entire deal pipeline                               ║
    ║                                                                              ║
    ║  📊 Monitor performance at: http://localhost:5000/api/v1/workflows/health    ║
    ║  🔧 Manual processing: http://localhost:5000/api/v1/workflows/process-lead   ║
    ║                                                                              ║
    ╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(status)

def display_usage_instructions():
    """Display usage instructions"""
    instructions = """
    ╔══════════════════════════════════════════════════════════════════════════════╗
    ║                            USAGE INSTRUCTIONS                               ║
    ╠══════════════════════════════════════════════════════════════════════════════╣
    ║                                                                              ║
    ║  🎯 AUTOMATIC OPERATION:                                                     ║
    ║     • System monitors GoHighLevel every 2 minutes for new Tier 1 leads      ║
    ║     • Hot leads trigger immediate processing and notifications               ║
    ║     • Follow-ups run automatically every 30 minutes                         ║
    ║                                                                              ║
    ║  🔧 MANUAL OPERATIONS:                                                       ║
    ║     • Process specific lead: POST /api/v1/workflows/process-lead/{id}        ║
    ║     • Trigger master pipeline: POST /api/v1/workflows/master-pipeline       ║
    ║     • Health check: GET /api/v1/workflows/health                             ║
    ║                                                                              ║
    ║  📊 MONITORING:                                                              ║
    ║     • System logs: logs/automation_controller.log                           ║
    ║     • Performance metrics: logs/automation_metrics.json                     ║
    ║     • Agent runs: logs/agent_runs.jsonl                                     ║
    ║                                                                              ║
    ║  🛑 TO STOP THE SYSTEM:                                                      ║
    ║     • Press Ctrl+C for graceful shutdown                                    ║
    ║                                                                              ║
    ╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(instructions)

async def main():
    """Main startup function"""
    try:
        # Display startup banner
        display_startup_banner()
        
        # Check prerequisites
        if not check_prerequisites():
            logger.error("❌ Prerequisites check failed. Please fix the issues and try again.")
            return
        
        # Import and start the automation controller
        logger.info("🚀 Starting Automation Controller...")
        from automation_controller import AutomationController
        
        controller = AutomationController()
        
        # Display system status
        display_system_status()
        
        # Display usage instructions
        display_usage_instructions()
        
        # Start the automation system
        await controller.start()
        
    except KeyboardInterrupt:
        logger.info("🛑 Received shutdown signal")
    except Exception as e:
        logger.error(f"❌ Startup failed: {e}")
        raise
    finally:
        logger.info("👋 System shutdown complete")

if __name__ == "__main__":
    # Ensure we're in the right directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Run the startup process
    asyncio.run(main())
