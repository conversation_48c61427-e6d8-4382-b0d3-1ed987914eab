#!/usr/bin/env bash
# Comprehensive Backup and Disaster Recovery System for AI-OS
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
BACKUP_DIR="${BACKUP_DIR:-/opt/ai-os-backups}"
RETENTION_DAYS="${RETENTION_DAYS:-30}"
S3_BUCKET="${S3_BUCKET:-}"
ENCRYPTION_KEY="${BACKUP_ENCRYPTION_KEY:-}"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="ai-os-backup-$TIMESTAMP"

# Load environment variables
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
fi

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Function to create database backup
backup_database() {
    log_info "Creating database backup..."
    
    local backup_file="$BACKUP_DIR/${BACKUP_NAME}_database.sql"
    
    if [ -n "${PG_CONN_STRING_READONLY:-}" ]; then
        # Backup Supabase database
        log_info "Backing up Supabase database..."
        pg_dump "$PG_CONN_STRING_READONLY" > "$backup_file"
        
        if [ $? -eq 0 ]; then
            log_success "Database backup created: $backup_file"
        else
            log_error "Database backup failed"
            return 1
        fi
    else
        log_warning "No database connection string found, skipping database backup"
    fi
    
    # Backup local PostgreSQL if running
    if docker-compose ps postgres | grep -q "Up"; then
        log_info "Backing up local PostgreSQL..."
        local local_backup="$BACKUP_DIR/${BACKUP_NAME}_local_postgres.sql"
        
        docker-compose exec -T postgres pg_dumpall -U retool > "$local_backup"
        
        if [ $? -eq 0 ]; then
            log_success "Local PostgreSQL backup created: $local_backup"
        else
            log_error "Local PostgreSQL backup failed"
        fi
    fi
}

# Function to backup Docker volumes
backup_volumes() {
    log_info "Creating Docker volumes backup..."
    
    local volumes_dir="$BACKUP_DIR/${BACKUP_NAME}_volumes"
    mkdir -p "$volumes_dir"
    
    # Backup PostgreSQL data
    if docker volume ls | grep -q "ai-os_postgres-data"; then
        log_info "Backing up PostgreSQL volume..."
        docker run --rm \
            -v ai-os_postgres-data:/data \
            -v "$volumes_dir":/backup \
            ubuntu tar czf /backup/postgres-data.tar.gz /data
        
        if [ $? -eq 0 ]; then
            log_success "PostgreSQL volume backup created"
        else
            log_error "PostgreSQL volume backup failed"
        fi
    fi
    
    # Backup Retool data
    if docker volume ls | grep -q "ai-os_retool-data"; then
        log_info "Backing up Retool volume..."
        docker run --rm \
            -v ai-os_retool-data:/data \
            -v "$volumes_dir":/backup \
            ubuntu tar czf /backup/retool-data.tar.gz /data
        
        if [ $? -eq 0 ]; then
            log_success "Retool volume backup created"
        else
            log_error "Retool volume backup failed"
        fi
    fi
}

# Function to backup application files
backup_application() {
    log_info "Creating application files backup..."
    
    local app_backup="$BACKUP_DIR/${BACKUP_NAME}_application.tar.gz"
    
    # Create application backup excluding unnecessary files
    tar czf "$app_backup" \
        --exclude='node_modules' \
        --exclude='.git' \
        --exclude='__pycache__' \
        --exclude='*.pyc' \
        --exclude='.env' \
        --exclude='logs' \
        --exclude='*.log' \
        --exclude='venv' \
        --exclude='.venv' \
        .
    
    if [ $? -eq 0 ]; then
        log_success "Application backup created: $app_backup"
    else
        log_error "Application backup failed"
        return 1
    fi
}

# Function to backup configuration files
backup_configuration() {
    log_info "Creating configuration backup..."
    
    local config_dir="$BACKUP_DIR/${BACKUP_NAME}_config"
    mkdir -p "$config_dir"
    
    # Backup environment file (without sensitive data)
    if [ -f .env ]; then
        # Create sanitized env file
        grep -v -E "(API_KEY|SECRET|PASSWORD|TOKEN)" .env > "$config_dir/env_sanitized" || true
        log_info "Sanitized environment file backed up"
    fi
    
    # Backup Docker Compose files
    cp docker-compose*.yml "$config_dir/" 2>/dev/null || true
    
    # Backup Caddy configuration
    cp Caddyfile "$config_dir/" 2>/dev/null || true
    
    # Backup Retool configurations
    if [ -d "retool" ]; then
        cp -r retool "$config_dir/"
    fi
    
    # Backup scripts
    if [ -d "scripts" ]; then
        cp -r scripts "$config_dir/"
    fi
    
    log_success "Configuration backup created"
}

# Function to encrypt backup
encrypt_backup() {
    if [ -z "$ENCRYPTION_KEY" ]; then
        log_warning "No encryption key provided, skipping encryption"
        return 0
    fi
    
    log_info "Encrypting backup files..."
    
    for file in "$BACKUP_DIR"/${BACKUP_NAME}*; do
        if [ -f "$file" ] && [[ ! "$file" == *.enc ]]; then
            openssl enc -aes-256-cbc -salt -in "$file" -out "${file}.enc" -k "$ENCRYPTION_KEY"
            
            if [ $? -eq 0 ]; then
                rm "$file"  # Remove unencrypted file
                log_info "Encrypted: $(basename "$file")"
            else
                log_error "Failed to encrypt: $(basename "$file")"
            fi
        fi
    done
    
    log_success "Backup encryption completed"
}

# Function to upload to S3
upload_to_s3() {
    if [ -z "$S3_BUCKET" ]; then
        log_warning "No S3 bucket configured, skipping cloud upload"
        return 0
    fi
    
    log_info "Uploading backup to S3..."
    
    # Check if AWS CLI is available
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI not found, cannot upload to S3"
        return 1
    fi
    
    # Upload all backup files
    for file in "$BACKUP_DIR"/${BACKUP_NAME}*; do
        if [ -f "$file" ]; then
            aws s3 cp "$file" "s3://$S3_BUCKET/ai-os-backups/$(basename "$file")"
            
            if [ $? -eq 0 ]; then
                log_info "Uploaded: $(basename "$file")"
            else
                log_error "Failed to upload: $(basename "$file")"
            fi
        fi
    done
    
    log_success "S3 upload completed"
}

# Function to clean old backups
cleanup_old_backups() {
    log_info "Cleaning up old backups (older than $RETENTION_DAYS days)..."
    
    # Clean local backups
    find "$BACKUP_DIR" -name "ai-os-backup-*" -mtime +$RETENTION_DAYS -delete
    
    # Clean S3 backups if configured
    if [ -n "$S3_BUCKET" ] && command -v aws &> /dev/null; then
        local cutoff_date=$(date -d "$RETENTION_DAYS days ago" +%Y-%m-%d)
        aws s3 ls "s3://$S3_BUCKET/ai-os-backups/" | while read -r line; do
            local file_date=$(echo "$line" | awk '{print $1}')
            local file_name=$(echo "$line" | awk '{print $4}')
            
            if [[ "$file_date" < "$cutoff_date" ]]; then
                aws s3 rm "s3://$S3_BUCKET/ai-os-backups/$file_name"
                log_info "Deleted old S3 backup: $file_name"
            fi
        done
    fi
    
    log_success "Cleanup completed"
}

# Function to verify backup integrity
verify_backup() {
    log_info "Verifying backup integrity..."
    
    local verification_failed=false
    
    # Check if backup files exist and are not empty
    for file in "$BACKUP_DIR"/${BACKUP_NAME}*; do
        if [ -f "$file" ]; then
            if [ ! -s "$file" ]; then
                log_error "Backup file is empty: $(basename "$file")"
                verification_failed=true
            else
                log_info "Verified: $(basename "$file") ($(du -h "$file" | cut -f1))"
            fi
        fi
    done
    
    if [ "$verification_failed" = true ]; then
        log_error "Backup verification failed"
        return 1
    else
        log_success "Backup verification passed"
        return 0
    fi
}

# Function to create backup manifest
create_manifest() {
    log_info "Creating backup manifest..."
    
    local manifest_file="$BACKUP_DIR/${BACKUP_NAME}_manifest.json"
    
    cat > "$manifest_file" << EOF
{
    "backup_name": "$BACKUP_NAME",
    "timestamp": "$TIMESTAMP",
    "date": "$(date -Iseconds)",
    "hostname": "$(hostname)",
    "ai_os_version": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
    "files": [
EOF
    
    local first=true
    for file in "$BACKUP_DIR"/${BACKUP_NAME}*; do
        if [ -f "$file" ] && [[ "$file" != *"manifest.json" ]]; then
            if [ "$first" = true ]; then
                first=false
            else
                echo "," >> "$manifest_file"
            fi
            
            local file_size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo "0")
            local file_hash=$(sha256sum "$file" 2>/dev/null | cut -d' ' -f1 || shasum -a 256 "$file" 2>/dev/null | cut -d' ' -f1 || echo "unknown")
            
            cat >> "$manifest_file" << EOF
        {
            "name": "$(basename "$file")",
            "size": $file_size,
            "sha256": "$file_hash"
        }
EOF
        fi
    done
    
    cat >> "$manifest_file" << EOF
    ]
}
EOF
    
    log_success "Backup manifest created"
}

# Function to send notification
send_notification() {
    local status="$1"
    local message="$2"
    
    # Email notification if configured
    if [ -n "${NOTIFICATION_EMAIL:-}" ] && command -v mail &> /dev/null; then
        echo "$message" | mail -s "AI-OS Backup $status" "$NOTIFICATION_EMAIL"
    fi
    
    # Slack notification if configured
    if [ -n "${SLACK_WEBHOOK_URL:-}" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"AI-OS Backup $status: $message\"}" \
            "$SLACK_WEBHOOK_URL" &>/dev/null || true
    fi
}

# Main backup function
run_backup() {
    log_info "Starting AI-OS backup process..."
    log_info "Backup name: $BACKUP_NAME"
    log_info "Backup directory: $BACKUP_DIR"
    
    local start_time=$(date +%s)
    local backup_success=true
    
    # Create backups
    backup_database || backup_success=false
    backup_volumes || backup_success=false
    backup_application || backup_success=false
    backup_configuration || backup_success=false
    
    # Create manifest
    create_manifest
    
    # Encrypt if configured
    encrypt_backup
    
    # Verify backup
    verify_backup || backup_success=false
    
    # Upload to cloud if configured
    upload_to_s3
    
    # Cleanup old backups
    cleanup_old_backups
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [ "$backup_success" = true ]; then
        local success_message="Backup completed successfully in ${duration}s. Files stored in $BACKUP_DIR"
        log_success "$success_message"
        send_notification "SUCCESS" "$success_message"
    else
        local error_message="Backup completed with errors in ${duration}s. Check logs for details."
        log_error "$error_message"
        send_notification "ERROR" "$error_message"
        exit 1
    fi
}

# Restore function
restore_backup() {
    local backup_name="$1"
    
    if [ -z "$backup_name" ]; then
        log_error "Backup name required for restore"
        echo "Usage: $0 restore <backup_name>"
        echo "Available backups:"
        ls -1 "$BACKUP_DIR" | grep "ai-os-backup-" | sed 's/_.*$//' | sort -u
        exit 1
    fi
    
    log_info "Starting restore process for backup: $backup_name"
    log_warning "This will overwrite current data. Continue? (y/N)"
    read -r confirmation
    
    if [[ ! "$confirmation" =~ ^[Yy]$ ]]; then
        log_info "Restore cancelled"
        exit 0
    fi
    
    # Stop services
    log_info "Stopping services..."
    docker-compose down
    
    # Restore database
    local db_backup="$BACKUP_DIR/${backup_name}_database.sql"
    if [ -f "$db_backup" ]; then
        log_info "Restoring database..."
        psql "$PG_CONN_STRING_READONLY" < "$db_backup"
    fi
    
    # Restore volumes
    local volumes_dir="$BACKUP_DIR/${backup_name}_volumes"
    if [ -d "$volumes_dir" ]; then
        log_info "Restoring Docker volumes..."
        
        if [ -f "$volumes_dir/postgres-data.tar.gz" ]; then
            docker volume rm ai-os_postgres-data 2>/dev/null || true
            docker volume create ai-os_postgres-data
            docker run --rm \
                -v ai-os_postgres-data:/data \
                -v "$volumes_dir":/backup \
                ubuntu tar xzf /backup/postgres-data.tar.gz -C /
        fi
        
        if [ -f "$volumes_dir/retool-data.tar.gz" ]; then
            docker volume rm ai-os_retool-data 2>/dev/null || true
            docker volume create ai-os_retool-data
            docker run --rm \
                -v ai-os_retool-data:/data \
                -v "$volumes_dir":/backup \
                ubuntu tar xzf /backup/retool-data.tar.gz -C /
        fi
    fi
    
    # Restore application
    local app_backup="$BACKUP_DIR/${backup_name}_application.tar.gz"
    if [ -f "$app_backup" ]; then
        log_info "Restoring application files..."
        tar xzf "$app_backup"
    fi
    
    # Start services
    log_info "Starting services..."
    docker-compose up -d
    
    log_success "Restore completed"
}

# Main script logic
case "${1:-backup}" in
    "backup")
        run_backup
        ;;
    "restore")
        restore_backup "${2:-}"
        ;;
    "list")
        log_info "Available backups:"
        ls -1 "$BACKUP_DIR" | grep "ai-os-backup-" | sed 's/_.*$//' | sort -u
        ;;
    "cleanup")
        cleanup_old_backups
        ;;
    *)
        echo "Usage: $0 {backup|restore|list|cleanup}"
        echo "  backup  - Create a new backup"
        echo "  restore - Restore from backup"
        echo "  list    - List available backups"
        echo "  cleanup - Clean up old backups"
        exit 1
        ;;
esac
