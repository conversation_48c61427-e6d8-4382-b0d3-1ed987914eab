-- Initialize database for AI-OS production deployment
-- This script is run automatically when PostgreSQL container starts

-- Note: The main database 'retool' is already created by the container
-- This script ensures proper permissions are set

-- Connect to retool database and grant schema privileges
\c retool;

-- Ensure retool user has all necessary privileges
GRANT ALL ON SCHEMA public TO retool;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO retool;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO retool;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO retool;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO retool;
