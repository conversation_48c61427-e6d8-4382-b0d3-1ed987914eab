-- Initialize Retool database
-- This script creates the necessary database and user for Retool

-- Create retool database
CREATE DATABASE retool;

-- Create retool user
CREATE USER retool WITH PASSWORD 'retoolpassword';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE retool TO retool;

-- Connect to retool database and grant schema privileges
\c retool;
GRANT ALL ON SCHEMA public TO retool;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO retool;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO retool;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO retool;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO retool;
