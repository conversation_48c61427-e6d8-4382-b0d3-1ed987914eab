#!/usr/bin/env bash
# Upload sample knowledge templates to AI-OS knowledge pipeline
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Load environment variables
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
fi

# Configuration
API_URL="${WEBHOOK_URL:-http://localhost:5002}"
TEMPLATES_DIR="knowledge_pipeline/sample_templates"

log_info "Starting sample knowledge upload to AI-OS..."
log_info "API URL: $API_URL"

# Check if templates directory exists
if [ ! -d "$TEMPLATES_DIR" ]; then
    log_error "Templates directory not found: $TEMPLATES_DIR"
    exit 1
fi

# Function to upload a document
upload_document() {
    local file_path="$1"
    local doc_type="$2"
    local priority="$3"
    local tags="$4"
    local description="$5"
    
    local filename=$(basename "$file_path")
    
    log_info "Uploading $filename..."
    
    # Create metadata JSON
    local metadata=$(cat <<EOF
{
    "document_type": "$doc_type",
    "priority": "$priority",
    "description": "$description",
    "uploaded_via": "script"
}
EOF
)
    
    # Upload the file
    local response=$(curl -s -w "%{http_code}" -o /tmp/upload_response.json \
        -X POST "$API_URL/api/v1/documents/ingest-file" \
        -F "file=@$file_path" \
        -F "tags=$tags" \
        -F "user_metadata=$metadata")
    
    if [ "$response" = "200" ] || [ "$response" = "201" ]; then
        log_success "$filename uploaded successfully"
        return 0
    else
        log_error "$filename upload failed (HTTP $response)"
        if [ -f /tmp/upload_response.json ]; then
            cat /tmp/upload_response.json
        fi
        return 1
    fi
}

# Test API connectivity
log_info "Testing API connectivity..."
if curl -s -f "$API_URL/api/v1/health" > /dev/null; then
    log_success "API is accessible"
else
    log_error "Cannot connect to API at $API_URL"
    log_error "Make sure the AI-OS API service is running"
    exit 1
fi

# Upload MAO Calculation Template
if [ -f "$TEMPLATES_DIR/mao_calculation_template.md" ]; then
    upload_document \
        "$TEMPLATES_DIR/mao_calculation_template.md" \
        "mao_calculation" \
        "critical" \
        "mao,calculation,wholesaling,formula,arv,repair costs" \
        "Complete MAO calculation methodology with step-by-step process, formulas, and real examples. Critical for accurate deal analysis."
else
    log_warning "MAO calculation template not found"
fi

# Upload Negotiation Strategy Template
if [ -f "$TEMPLATES_DIR/negotiation_strategy_template.md" ]; then
    upload_document \
        "$TEMPLATES_DIR/negotiation_strategy_template.md" \
        "negotiation_strategy" \
        "high" \
        "negotiation,strategy,objection handling,seller types,tactics" \
        "Comprehensive negotiation strategies for different seller types, objection handling scripts, and advanced tactics for closing deals."
else
    log_warning "Negotiation strategy template not found"
fi

# Check for additional templates in the directory
log_info "Checking for additional templates..."
template_count=0
for file in "$TEMPLATES_DIR"/*.md; do
    if [ -f "$file" ]; then
        filename=$(basename "$file")
        case "$filename" in
            "mao_calculation_template.md"|"negotiation_strategy_template.md")
                # Already processed above
                ;;
            *"market_analysis"*)
                upload_document \
                    "$file" \
                    "market_analysis" \
                    "medium" \
                    "market analysis,location,criteria,indicators" \
                    "Market analysis guidelines and criteria for evaluating locations and market conditions."
                ((template_count++))
                ;;
            *"deal_analysis"*)
                upload_document \
                    "$file" \
                    "deal_analysis" \
                    "high" \
                    "deal analysis,evaluation,checklist,criteria" \
                    "Complete deal analysis template with evaluation criteria and decision-making framework."
                ((template_count++))
                ;;
            *"contract"*)
                upload_document \
                    "$file" \
                    "contract_template" \
                    "medium" \
                    "contract,legal,template,clauses" \
                    "Contract templates and legal clauses for real estate transactions."
                ((template_count++))
                ;;
            *"follow"*|*"sequence"*)
                upload_document \
                    "$file" \
                    "follow_up_sequence" \
                    "medium" \
                    "follow up,sequence,communication,timeline" \
                    "Follow-up sequences and communication templates for lead nurturing."
                ((template_count++))
                ;;
            *)
                upload_document \
                    "$file" \
                    "general_knowledge" \
                    "low" \
                    "general,knowledge,reference" \
                    "General knowledge document for AI learning and reference."
                ((template_count++))
                ;;
        esac
    fi
done

# Upload any PDF or TXT files as general knowledge
for file in "$TEMPLATES_DIR"/*.pdf "$TEMPLATES_DIR"/*.txt; do
    if [ -f "$file" ]; then
        upload_document \
            "$file" \
            "general_knowledge" \
            "medium" \
            "reference,document,knowledge" \
            "Reference document uploaded for AI knowledge base."
        ((template_count++))
    fi
done

log_info "Upload process completed!"
echo ""
log_info "Summary:"
echo "  - MAO Calculation Template: $([ -f "$TEMPLATES_DIR/mao_calculation_template.md" ] && echo "✅ Uploaded" || echo "❌ Not found")"
echo "  - Negotiation Strategy Template: $([ -f "$TEMPLATES_DIR/negotiation_strategy_template.md" ] && echo "✅ Uploaded" || echo "❌ Not found")"
echo "  - Additional templates: $template_count uploaded"

echo ""
log_info "Next steps:"
echo "1. Access your Retool Knowledge Management dashboard"
echo "2. Verify documents were uploaded successfully"
echo "3. Review document analytics and usage"
echo "4. Add your own custom knowledge documents"

echo ""
log_info "Knowledge Management Dashboard: $API_URL/retool (if using production URL)"
log_info "Local Dashboard: http://localhost:3000/retool"

# Clean up
rm -f /tmp/upload_response.json

log_success "Sample knowledge upload completed! 🧠"
