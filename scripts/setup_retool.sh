#!/usr/bin/env bash
# Complete Retool setup script for AI-OS
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Load environment variables
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
fi

# Configuration
RT_URL="${RETOOL_HOSTNAME:-http://localhost:3000}"
RT_KEY="${RETOOL_API_KEY:-}"

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    
    # Check if required files exist
    if [[ ! -f "retool/lead_manager_export.json" ]]; then
        log_error "Retool dashboard file not found: retool/lead_manager_export.json"
        exit 1
    fi
    
    if [[ ! -f "retool/resources/supabase_prod.json" ]]; then
        log_error "Supabase resource file not found: retool/resources/supabase_prod.json"
        exit 1
    fi
    
    # Check environment variables
    if [[ -z "$RT_KEY" ]]; then
        log_warning "RETOOL_API_KEY not set. You'll need to set this after Retool starts."
    fi
    
    if [[ -z "${PG_CONN_STRING_READONLY:-}" ]]; then
        log_error "PG_CONN_STRING_READONLY not set in .env file"
        exit 1
    fi
    
    if [[ -z "${GHL_API_KEY:-}" ]]; then
        log_error "GHL_API_KEY not set in .env file"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Start Retool service
start_retool() {
    log_info "Starting Retool service..."
    
    # Stop existing Retool container if running
    docker-compose stop retool 2>/dev/null || true
    docker-compose rm -f retool 2>/dev/null || true
    
    # Start Retool
    docker-compose up -d retool
    
    log_info "Waiting for Retool to start..."
    sleep 30
    
    log_success "Retool service started"
}

# Wait for Retool to be ready
wait_for_retool() {
    log_info "Waiting for Retool to be ready at $RT_URL..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$RT_URL/api/checkHealth" > /dev/null 2>&1; then
            log_success "Retool is ready!"
            return 0
        fi
        log_info "Attempt $attempt/$max_attempts: Retool not ready yet, waiting 10 seconds..."
        sleep 10
        ((attempt++))
    done
    
    log_error "Retool failed to become ready after $max_attempts attempts"
    return 1
}

# Check if this is first time setup
check_first_time_setup() {
    log_info "Checking if this is first-time setup..."
    
    # Try to access the API without authentication to see if setup is needed
    local response=$(curl -s -w "%{http_code}" -o /dev/null "$RT_URL/api/checkHealth" || echo "000")
    
    if [[ "$response" == "401" ]] || [[ "$response" == "403" ]]; then
        log_info "Retool appears to be set up already"
        return 1
    elif [[ "$response" == "200" ]]; then
        log_info "Retool is accessible - checking if admin user exists"
        return 0
    else
        log_warning "Retool status unclear (HTTP $response). Proceeding with setup..."
        return 0
    fi
}

# Display setup instructions
show_setup_instructions() {
    log_info "=== RETOOL FIRST-TIME SETUP REQUIRED ==="
    echo ""
    echo "1. Open your browser and go to: $RT_URL"
    echo "2. Create an admin account when prompted"
    echo "3. Log in to your new Retool instance"
    echo "4. Go to Settings → API and create a new API key"
    echo "5. Add the API key to your .env file:"
    echo "   RETOOL_API_KEY=your_api_key_here"
    echo "6. Run this script again to import the dashboard and resources"
    echo ""
    log_warning "Press Enter after completing the setup, or Ctrl+C to exit"
    read -r
}

# Import resources and dashboard
import_dashboard() {
    log_info "Importing Retool resources and dashboard..."
    
    if [[ -z "$RT_KEY" ]]; then
        log_error "RETOOL_API_KEY is required for import. Please set it in .env and try again."
        exit 1
    fi
    
    # Import Supabase resource
    log_info "Importing Supabase resource..."
    if curl --fail -sS -X POST "$RT_URL/api/resources" \
        -H "Authorization: Bearer $RT_KEY" \
        -H "Content-Type: application/json" \
        --data-binary "@retool/resources/supabase_prod.json" > /dev/null 2>&1; then
        log_success "Supabase resource imported successfully"
    else
        log_warning "Supabase resource import failed (may already exist)"
    fi
    
    # Import dashboard
    log_info "Importing AI-OS dashboard..."
    if curl --fail -sS -X POST "$RT_URL/api/apps/import" \
        -H "Authorization: Bearer $RT_KEY" \
        -F "file=@retool/lead_manager_export.json" > /dev/null 2>&1; then
        log_success "Dashboard imported successfully"
    else
        log_warning "Dashboard import failed (may already exist)"
    fi
}

# Main execution
main() {
    log_info "Starting Retool setup for AI-OS..."
    
    check_prerequisites
    start_retool
    wait_for_retool
    
    if check_first_time_setup; then
        show_setup_instructions
        # Reload environment in case user updated .env
        if [ -f .env ]; then
            export $(grep -v '^#' .env | xargs)
            RT_KEY="${RETOOL_API_KEY:-}"
        fi
    fi
    
    if [[ -n "$RT_KEY" ]]; then
        import_dashboard
        log_success "Retool setup completed! Access your dashboard at: $RT_URL"
    else
        log_warning "Skipping import due to missing API key. Run script again after setting RETOOL_API_KEY."
    fi
}

# Run main function
main "$@"
