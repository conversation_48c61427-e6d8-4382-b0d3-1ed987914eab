#!/usr/bin/env bash
# Update environment variables for production deployment
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if domain is provided
if [ $# -eq 0 ]; then
    log_error "Usage: $0 <domain>"
    log_info "Example: $0 yourdomain.com"
    exit 1
fi

DOMAIN=$1
USE_SSL=true

# Check if domain is an IP address
if [[ $DOMAIN =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    USE_SSL=false
    log_warning "IP address detected. SSL will be disabled."
fi

log_info "Updating environment for production deployment"
log_info "Domain: $DOMAIN"
log_info "SSL: $USE_SSL"

# Backup original .env file
if [ -f .env ]; then
    cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
    log_info "Backed up original .env file"
fi

# Update environment variables for production
log_info "Updating environment variables..."

if [ "$USE_SSL" = true ]; then
    PROTOCOL="https"
else
    PROTOCOL="http"
fi

# Update webhook URLs
sed -i.bak "s|http://localhost:5678|$PROTOCOL://$DOMAIN|g" .env
sed -i.bak "s|https://localhost:5678|$PROTOCOL://$DOMAIN|g" .env

# Update Retool hostname
sed -i.bak "s|RETOOL_HOSTNAME=.*|RETOOL_HOSTNAME=$PROTOCOL://$DOMAIN|g" .env

# Update webhook URLs with proper paths
sed -i.bak "s|N8N_WEBHOOK_URL=.*|N8N_WEBHOOK_URL=$PROTOCOL://$DOMAIN/webhook/ghl/lead|g" .env
sed -i.bak "s|N8N_ALERT_WEBHOOK_URL=.*|N8N_ALERT_WEBHOOK_URL=$PROTOCOL://$DOMAIN/webhook-test/util/ghl-alert|g" .env

# Update any localhost references
sed -i.bak "s|localhost:5678|$DOMAIN|g" .env
sed -i.bak "s|localhost:5002|$DOMAIN|g" .env
sed -i.bak "s|localhost:3000|$DOMAIN|g" .env

# Generate secure secrets for production if they're still default
if grep -q "retool-jwt-secret-change-me" .env; then
    NEW_JWT_SECRET=$(openssl rand -base64 32)
    sed -i.bak "s|RETOOL_JWT_SECRET=.*|RETOOL_JWT_SECRET=$NEW_JWT_SECRET|g" .env
    log_info "Generated new JWT secret for Retool"
fi

if grep -q "retool-encryption-key-change-me" .env; then
    NEW_ENCRYPTION_KEY=$(openssl rand -base64 32)
    sed -i.bak "s|RETOOL_ENCRYPTION_KEY=.*|RETOOL_ENCRYPTION_KEY=$NEW_ENCRYPTION_KEY|g" .env
    log_info "Generated new encryption key for Retool"
fi

# Clean up backup files
rm -f .env.bak

log_success "Environment updated for production deployment"
log_info "Updated URLs:"
echo "  - API: $PROTOCOL://$DOMAIN/api/v1/health"
echo "  - n8n: $PROTOCOL://$DOMAIN/n8n"
echo "  - Retool: $PROTOCOL://$DOMAIN/retool"
echo "  - Webhooks: $PROTOCOL://$DOMAIN/webhook/ghl/lead"

log_warning "Remember to:"
echo "1. Update GHL webhooks to use the new domain"
echo "2. Update any external integrations with the new URLs"
echo "3. Test all endpoints after deployment"
