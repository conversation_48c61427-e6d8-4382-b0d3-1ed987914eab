#!/usr/bin/env bash
# Test script for streamlined AI-OS deployment
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
BASE_URL="${1:-http://localhost}"
RETOOL_PORT="${2:-3000}"
API_PORT="${3:-5002}"

log_info "Testing streamlined AI-OS deployment"
log_info "Base URL: $BASE_URL"
echo ""

# Test 1: API Health Check
log_info "Testing AI-OS API health..."
if curl -f -s "$BASE_URL:$API_PORT/api/v1/health" > /dev/null; then
    log_success "AI-OS API is healthy"
else
    log_error "AI-OS API health check failed"
    exit 1
fi

# Test 2: Retool Health Check
log_info "Testing Retool health..."
if curl -f -s "$BASE_URL:$RETOOL_PORT/api/checkHealth" > /dev/null; then
    log_success "Retool is healthy"
else
    log_error "Retool health check failed"
    exit 1
fi

# Test 3: Webhook Endpoint
log_info "Testing webhook endpoint..."
webhook_response=$(curl -s -w "%{http_code}" -o /dev/null -X POST "$BASE_URL:$API_PORT/webhook/ghl/lead" \
    -H "Content-Type: application/json" \
    -d '{"test": "data"}')

if [ "$webhook_response" = "200" ] || [ "$webhook_response" = "400" ]; then
    log_success "Webhook endpoint is responding"
else
    log_error "Webhook endpoint test failed (HTTP $webhook_response)"
fi

# Test 4: Master Pipeline Endpoint
log_info "Testing master pipeline endpoint..."
pipeline_response=$(curl -s -w "%{http_code}" -o /dev/null -X POST "$BASE_URL:$API_PORT/api/v1/workflows/master-pipeline" \
    -H "Content-Type: application/json" \
    -d '{"lead_id": "test", "lead_data": {"test": true}}')

if [ "$pipeline_response" = "200" ] || [ "$pipeline_response" = "400" ] || [ "$pipeline_response" = "422" ]; then
    log_success "Master pipeline endpoint is responding"
else
    log_error "Master pipeline endpoint test failed (HTTP $pipeline_response)"
fi

# Test 5: Docker Services Status
log_info "Checking Docker services..."
if command -v docker-compose &> /dev/null; then
    if docker-compose ps | grep -q "Up"; then
        log_success "Docker services are running"
        echo ""
        docker-compose ps
    else
        log_warning "Some Docker services may not be running"
        docker-compose ps
    fi
else
    log_warning "Docker Compose not available for service check"
fi

echo ""
log_success "Streamlined deployment test completed!"
echo ""
log_info "Access URLs:"
echo "  - Retool Dashboard: $BASE_URL:$RETOOL_PORT"
echo "  - AI-OS API: $BASE_URL:$API_PORT/api/v1/health"
echo "  - Webhook URL: $BASE_URL:$API_PORT/webhook/ghl/lead"
echo ""
log_info "Next steps:"
echo "1. Access Retool dashboard and complete setup"
echo "2. Update GHL webhooks to use the webhook URL above"
echo "3. Monitor logs with: docker-compose logs -f"
