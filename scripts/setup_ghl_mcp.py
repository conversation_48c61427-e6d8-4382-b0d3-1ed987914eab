#!/usr/bin/env python3
"""
Setup script for GoHighLevel MCP Server integration.
This script helps configure the environment variables and validates the setup.
"""

import os
import sys
import json
import subprocess
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_ghl_credentials():
    """Check if GoHighLevel credentials are properly configured."""
    print("🔍 Checking GoHighLevel credentials...")

    ghl_api_key = os.getenv("GHL_API_KEY")
    ghl_location_id = os.getenv("GHL_LOCATION_ID")

    if not ghl_api_key:
        print("❌ GHL_API_KEY not found in environment variables")
        print("📋 Please set up your Location API key:")
        print("   1. Go to your GoHighLevel sub-account (location)")
        print("   2. Navigate to: Settings → Company → API Key")
        print("   3. Copy your Location API Key")
        print("   4. Set GHL_API_KEY in your .env file")
        print("   💡 Note: Use Location API Key, NOT Private Integrations (for Starter Plan)")
        return False
    
    if not ghl_location_id:
        print("❌ GHL_LOCATION_ID not found in environment variables")
        print("📋 Please get your Location ID:")
        print("   1. Go to GoHighLevel → Settings → Company → API Key")
        print("   2. Copy your Location ID (shown on the same page)")
        print("   3. Set GHL_LOCATION_ID in your .env file")
        return False
    
    print("✅ GoHighLevel credentials configured")
    return True

def setup_mcp_environment():
    """Set up the MCP server environment."""
    print("🔧 Setting up MCP server environment...")
    
    mcp_env_path = Path("ghl-mcp-server/.env")
    
    # Read current environment variables
    ghl_api_key = os.getenv("GHL_API_KEY")
    ghl_location_id = os.getenv("GHL_LOCATION_ID")

    if ghl_api_key and ghl_location_id:
        # Update the MCP server .env file
        env_content = f"""# GoHighLevel MCP Server Configuration
# For Starter Plan ($97/month): Use Location API Key

# GoHighLevel API Configuration
GHL_API_KEY={ghl_api_key}
GHL_BASE_URL=https://rest.gohighlevel.com/v1
GHL_LOCATION_ID={ghl_location_id}

# MCP Server Configuration
MCP_SERVER_PORT=8000
NODE_ENV=production

# Optional: OpenAI API Key for enhanced features
OPENAI_API_KEY={os.getenv('OPENAI_API_KEY', 'your_openai_api_key_here')}

# CORS Configuration
CORS_ORIGINS=*

# Logging
LOG_LEVEL=info
"""
        
        with open(mcp_env_path, 'w') as f:
            f.write(env_content)
        
        print(f"✅ MCP environment configured at {mcp_env_path}")
        return True
    else:
        print("❌ Missing required environment variables")
        return False

def build_mcp_server():
    """Build the MCP server."""
    print("🔨 Building MCP server...")
    
    try:
        result = subprocess.run(
            ["npm", "run", "build"],
            cwd="ghl-mcp-server",
            capture_output=True,
            text=True,
            check=True
        )
        print("✅ MCP server built successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to build MCP server: {e}")
        print(f"Error output: {e.stderr}")
        return False

def test_mcp_server():
    """Test the MCP server connectivity."""
    print("🧪 Testing MCP server...")
    
    try:
        # Start the server in the background for testing
        process = subprocess.Popen(
            ["npm", "run", "start:http"],
            cwd="ghl-mcp-server",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Give it a moment to start
        import time
        time.sleep(3)
        
        # Test health endpoint
        import requests
        response = requests.get("http://localhost:8000/health", timeout=5)
        
        if response.status_code == 200:
            print("✅ MCP server is running and responding")
            success = True
        else:
            print(f"❌ MCP server responded with status {response.status_code}")
            success = False
        
        # Clean up
        process.terminate()
        process.wait()
        
        return success
        
    except Exception as e:
        print(f"❌ Failed to test MCP server: {e}")
        return False

def create_claude_desktop_config():
    """Create Claude Desktop MCP configuration."""
    print("🖥️ Creating Claude Desktop configuration...")
    
    # Get the absolute path to the MCP server
    mcp_server_path = Path("ghl-mcp-server/dist/server.js").absolute()
    
    claude_config = {
        "mcpServers": {
            "gohighlevel-comprehensive": {
                "command": "node",
                "args": [str(mcp_server_path)],
                "env": {
                    "GHL_API_KEY": os.getenv("GHL_PRIVATE_INTEGRATIONS_API_KEY") or os.getenv("GHL_API_KEY"),
                    "GHL_BASE_URL": "https://services.leadconnectorhq.com",
                    "GHL_LOCATION_ID": os.getenv("GHL_LOCATION_ID"),
                    "NODE_ENV": "production"
                }
            }
        }
    }
    
    config_path = Path("claude_desktop_mcp_config.json")
    with open(config_path, 'w') as f:
        json.dump(claude_config, f, indent=2)
    
    print(f"✅ Claude Desktop config created at {config_path}")
    print("📋 To use with Claude Desktop:")
    print("   1. Copy the contents of claude_desktop_mcp_config.json")
    print("   2. Add it to your Claude Desktop mcp_settings.json file")
    print("   3. Restart Claude Desktop")
    
    return True

def main():
    """Main setup function."""
    print("🚀 GoHighLevel MCP Server Setup")
    print("=" * 50)
    
    success = True
    
    # Check credentials
    if not check_ghl_credentials():
        success = False
    
    # Setup environment
    if success and not setup_mcp_environment():
        success = False
    
    # Build server
    if success and not build_mcp_server():
        success = False
    
    # Test server
    if success and not test_mcp_server():
        print("⚠️ Server test failed, but setup may still be valid")
    
    # Create Claude config
    if success:
        create_claude_desktop_config()
    
    if success:
        print("\n🎉 Setup completed successfully!")
        print("📋 Next steps:")
        print("   1. Configure Claude Desktop with the generated config")
        print("   2. Test the integration with Claude")
        print("   3. Start using the 269+ GoHighLevel tools!")
    else:
        print("\n❌ Setup failed. Please check the errors above and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
