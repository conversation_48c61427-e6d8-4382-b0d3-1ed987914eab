#!/bin/bash

# GoHighLevel MCP Server Deployment Script
# Deploys the comprehensive MCP server for production use

set -e

echo "🚀 GoHighLevel MCP Server Deployment"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Check if we're in the right directory
if [ ! -d "ghl-mcp-server" ]; then
    print_error "ghl-mcp-server directory not found. Please run this script from the project root."
    exit 1
fi

# Check environment variables
print_info "Checking environment variables..."

if [ -z "$GHL_PRIVATE_INTEGRATIONS_API_KEY" ] && [ -z "$GHL_API_KEY" ]; then
    print_error "GHL_PRIVATE_INTEGRATIONS_API_KEY or GHL_API_KEY not set"
    print_info "Please set your GoHighLevel Private Integrations API key"
    exit 1
fi

if [ -z "$GHL_LOCATION_ID" ]; then
    print_error "GHL_LOCATION_ID not set"
    print_info "Please set your GoHighLevel Location ID"
    exit 1
fi

print_status "Environment variables configured"

# Install dependencies
print_info "Installing MCP server dependencies..."
cd ghl-mcp-server
npm install
print_status "Dependencies installed"

# Build the project
print_info "Building MCP server..."
npm run build
print_status "MCP server built successfully"

# Create production environment file
print_info "Creating production environment configuration..."
cat > .env << EOF
# GoHighLevel MCP Server Configuration - Production
GHL_API_KEY=${GHL_PRIVATE_INTEGRATIONS_API_KEY:-$GHL_API_KEY}
GHL_BASE_URL=https://services.leadconnectorhq.com
GHL_LOCATION_ID=${GHL_LOCATION_ID}
MCP_SERVER_PORT=8000
NODE_ENV=production
CORS_ORIGINS=*
LOG_LEVEL=info
EOF

print_status "Production environment configured"

# Test the build
print_info "Testing MCP server build..."
if node dist/server.js --help > /dev/null 2>&1; then
    print_status "MCP server build test passed"
else
    print_warning "MCP server build test failed, but continuing..."
fi

cd ..

# Create systemd service file for production deployment
print_info "Creating systemd service file..."
cat > ghl-mcp-server.service << EOF
[Unit]
Description=GoHighLevel MCP Server
After=network.target

[Service]
Type=simple
User=\$USER
WorkingDirectory=$(pwd)/ghl-mcp-server
Environment=NODE_ENV=production
ExecStart=/usr/bin/node dist/http-server.js
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

print_status "Systemd service file created"

# Create Docker configuration
print_info "Creating Docker configuration..."
cat > ghl-mcp-server/Dockerfile.production << EOF
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy built application
COPY dist/ ./dist/

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

USER nodejs

EXPOSE 8000

CMD ["node", "dist/http-server.js"]
EOF

print_status "Docker configuration created"

# Create docker-compose for easy deployment
print_info "Creating Docker Compose configuration..."
cat > docker-compose.ghl-mcp.yml << EOF
version: '3.8'

services:
  ghl-mcp-server:
    build:
      context: ./ghl-mcp-server
      dockerfile: Dockerfile.production
    ports:
      - "8000:8000"
    environment:
      - GHL_API_KEY=${GHL_PRIVATE_INTEGRATIONS_API_KEY:-$GHL_API_KEY}
      - GHL_BASE_URL=https://services.leadconnectorhq.com
      - GHL_LOCATION_ID=${GHL_LOCATION_ID}
      - NODE_ENV=production
      - MCP_SERVER_PORT=8000
      - CORS_ORIGINS=*
      - LOG_LEVEL=info
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
EOF

print_status "Docker Compose configuration created"

# Create Claude Desktop configuration
print_info "Creating Claude Desktop configuration..."
cat > claude-desktop-mcp-config.json << EOF
{
  "mcpServers": {
    "gohighlevel-comprehensive": {
      "command": "node",
      "args": ["$(pwd)/ghl-mcp-server/dist/server.js"],
      "env": {
        "GHL_API_KEY": "${GHL_PRIVATE_INTEGRATIONS_API_KEY:-$GHL_API_KEY}",
        "GHL_BASE_URL": "https://services.leadconnectorhq.com",
        "GHL_LOCATION_ID": "${GHL_LOCATION_ID}",
        "NODE_ENV": "production"
      }
    }
  }
}
EOF

print_status "Claude Desktop configuration created"

# Create startup script
print_info "Creating startup script..."
cat > start-ghl-mcp.sh << EOF
#!/bin/bash

echo "🚀 Starting GoHighLevel MCP Server..."

cd ghl-mcp-server

# Check if built
if [ ! -d "dist" ]; then
    echo "Building MCP server..."
    npm run build
fi

# Start the server
echo "Starting HTTP MCP server on port 8000..."
npm run start:http
EOF

chmod +x start-ghl-mcp.sh
print_status "Startup script created"

# Create monitoring script
print_info "Creating monitoring script..."
cat > monitor-ghl-mcp.sh << EOF
#!/bin/bash

# Simple monitoring script for GHL MCP Server

check_server() {
    if curl -f -s http://localhost:8000/health > /dev/null; then
        echo "✅ GHL MCP Server is running"
        return 0
    else
        echo "❌ GHL MCP Server is not responding"
        return 1
    fi
}

check_tools() {
    local tool_count=\$(curl -s http://localhost:8000/tools | jq '.tools | length' 2>/dev/null)
    if [ "\$tool_count" -gt 200 ]; then
        echo "✅ \$tool_count tools available"
        return 0
    else
        echo "⚠️ Only \$tool_count tools available (expected 269+)"
        return 1
    fi
}

echo "🔍 GHL MCP Server Health Check"
echo "=============================="

check_server
check_tools

echo ""
echo "📊 Server Status:"
curl -s http://localhost:8000/health | jq . 2>/dev/null || echo "Could not get detailed status"
EOF

chmod +x monitor-ghl-mcp.sh
print_status "Monitoring script created"

# Final instructions
echo ""
echo "🎉 GoHighLevel MCP Server Deployment Complete!"
echo "=============================================="
echo ""
print_info "Deployment Options:"
echo ""
echo "1. 🖥️  Local Development:"
echo "   ./start-ghl-mcp.sh"
echo ""
echo "2. 🐳 Docker Deployment:"
echo "   docker-compose -f docker-compose.ghl-mcp.yml up -d"
echo ""
echo "3. 🔧 Systemd Service (Linux):"
echo "   sudo cp ghl-mcp-server.service /etc/systemd/system/"
echo "   sudo systemctl enable ghl-mcp-server"
echo "   sudo systemctl start ghl-mcp-server"
echo ""
echo "4. ☁️  Cloud Deployment:"
echo "   - Vercel: Deploy ghl-mcp-server directory"
echo "   - Railway: Use railway.json configuration"
echo "   - Render: Use Dockerfile.production"
echo ""
print_info "Claude Desktop Integration:"
echo "   Copy contents of claude-desktop-mcp-config.json to your Claude Desktop mcp_settings.json"
echo ""
print_info "Monitoring:"
echo "   ./monitor-ghl-mcp.sh"
echo ""
print_status "Ready to revolutionize your GoHighLevel automation! 🚀"
