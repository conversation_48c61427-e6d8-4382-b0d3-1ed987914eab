
#!/usr/bin/env bash
# Import Retool resources & dashboard once the container is up
set -euo pipefail

# Load environment variables
if [ -f .env ]; then
  export $(grep -v '^#' .env | xargs)
fi

# Determine Retool URL and API Key from environment variables, with defaults
RT_URL="${RETOOL_HOSTNAME:-http://localhost:3000}"
RT_KEY="${RETOOL_API_KEY:-}"

# Check if RETOOL_API_KEY is set
if [[ -z "$RT_KEY" ]]; then
  echo "[retool] ❌ RETOOL_API_KEY not set in env. Please set it and re-run."
  echo "[retool] You can get an API key from your Retool instance under Settings -> API."
  exit 1
fi

# Function to wait for Retool to be ready
wait_for_retool() {
  echo "[retool] Waiting for Retool to be ready at $RT_URL..."
  local max_attempts=30
  local attempt=1

  while [ $attempt -le $max_attempts ]; do
    if curl -s -f "$RT_URL/api/checkHealth" > /dev/null 2>&1; then
      echo "[retool] ✅ Retool is ready!"
      return 0
    fi
    echo "[retool] Attempt $attempt/$max_attempts: Retool not ready yet, waiting 10 seconds..."
    sleep 10
    ((attempt++))
  done

  echo "[retool] ❌ Retool failed to become ready after $max_attempts attempts"
  return 1
fi

# Wait for Retool to be ready
wait_for_retool

# Ensure the script is run from the project root by checking for expected file paths
# This makes relative paths like "@retool/resources/supabase_prod.json" reliable.
if [[ ! -f "retool/resources/supabase_prod.json" || ! -f "retool/lead_manager_export.json" ]]; then
    echo "[retool] ❌ Error: Script must be run from the project root directory, or Retool files are missing."
    echo "[retool] Current directory: $(pwd)"
    echo "[retool] Expected files: retool/resources/supabase_prod.json and retool/lead_manager_export.json"
    exit 1
fi

echo "[retool] Attempting to import Supabase resource to Retool instance at $RT_URL..."
# The --fail flag makes curl exit with an error if the HTTP request fails (e.g., 4xx or 5xx)
# The -sS flags make curl silent but still show errors.
# Using a temporary file for curl output to check for Retool's specific success/error messages if needed.
curl_output_resource=$(mktemp)
if curl --fail -sS -X POST "$RT_URL/api/resources" \
  -H "Authorization: Bearer $RT_KEY" \
  -H "Content-Type: application/json" \
  --data-binary "@retool/resources/supabase_prod.json" \
  -o "$curl_output_resource"; then
  echo "[retool] Supabase resource import API call successful."
  # Optionally, inspect $curl_output_resource for more specific success indicators from Retool's API
  # cat "$curl_output_resource"
else
  echo "[retool] ❌ ERROR: Supabase resource import API call failed. Response:"
  cat "$curl_output_resource"
  rm "$curl_output_resource"
  # exit 1 # Optionally exit on first failure
fi
rm "$curl_output_resource"
echo "" # Newline for better output separation

echo "[retool] Attempting to import AI-OS dashboard to Retool instance at $RT_URL..."
curl_output_app=$(mktemp)
if curl --fail -sS -X POST "$RT_URL/api/apps/import" \
  -H "Authorization: Bearer $RT_KEY" \
  -F "file=@retool/lead_manager_export.json" \
  -o "$curl_output_app"; then
  echo "[retool] AI-OS dashboard import API call successful."
  # Optionally, inspect $curl_output_app
  # cat "$curl_output_app"
else
  echo "[retool] ❌ ERROR: AI-OS dashboard import API call failed. Response:"
  cat "$curl_output_app"
  rm "$curl_output_app"
  # exit 1 # Optionally exit if this failure is critical
fi
rm "$curl_output_app"
echo "" # Newline for better output separation

echo "[retool] ✅ Retool import process attempted. Review output above for success/failure details from curl."
