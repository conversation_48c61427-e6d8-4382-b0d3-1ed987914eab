#!/usr/bin/env python3
"""
Comprehensive test script for GoHighLevel MCP integration.
Tests the 269+ tools and real estate workflow automation.
"""

import os
import sys
import json
import time
import subprocess
from pathlib import Path
from dotenv import load_dotenv

# Add the project root to the path
sys.path.append(str(Path(__file__).parent.parent))

from agents.ghl_mcp_client import GHLMCPClient

# Load environment variables
load_dotenv()

def start_mcp_server():
    """Start the MCP server for testing."""
    print("🚀 Starting MCP server...")
    
    try:
        process = subprocess.Popen(
            ["npm", "run", "start:http"],
            cwd="ghl-mcp-server",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Give it time to start
        time.sleep(5)
        
        # Test if it's running
        import requests
        response = requests.get("http://localhost:8000/health", timeout=5)
        
        if response.status_code == 200:
            print("✅ MCP server started successfully")
            return process
        else:
            print(f"❌ MCP server not responding properly: {response.status_code}")
            process.terminate()
            return None
            
    except Exception as e:
        print(f"❌ Failed to start MCP server: {e}")
        return None

def test_basic_connectivity():
    """Test basic MCP server connectivity."""
    print("\n🔍 Testing basic connectivity...")
    
    try:
        client = GHLMCPClient()
        print("✅ MCP client initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to initialize MCP client: {e}")
        return False

def test_contact_management():
    """Test contact management tools."""
    print("\n👥 Testing contact management...")
    
    try:
        client = GHLMCPClient()
        
        # Test contact creation
        contact_data = {
            "firstName": "Test",
            "lastName": "Lead",
            "email": "<EMAIL>",
            "phone": "+1234567890",
            "address": "123 Test St, Test City, TS 12345"
        }
        
        result = client.create_contact(**contact_data)
        
        if "error" in result:
            print(f"⚠️ Contact creation test: {result.get('error', 'Unknown error')}")
            return False
        else:
            print("✅ Contact creation test passed")
            return True
            
    except Exception as e:
        print(f"❌ Contact management test failed: {e}")
        return False

def test_messaging_tools():
    """Test messaging and conversation tools."""
    print("\n💬 Testing messaging tools...")
    
    try:
        client = GHLMCPClient()
        
        # Test SMS sending (will fail without valid contact ID, but tests tool call)
        result = client.send_sms(
            contact_id="test_contact_id",
            message="Test message from MCP integration"
        )
        
        # We expect this to fail with invalid contact ID, but the tool should be callable
        if "error" in result:
            print("✅ SMS tool callable (expected error with test contact ID)")
            return True
        else:
            print("✅ SMS tool test passed")
            return True
            
    except Exception as e:
        print(f"❌ Messaging test failed: {e}")
        return False

def test_opportunity_management():
    """Test opportunity and pipeline management."""
    print("\n💰 Testing opportunity management...")
    
    try:
        client = GHLMCPClient()
        
        # Test opportunity search
        result = client.search_opportunities()
        
        if "error" in result:
            print(f"⚠️ Opportunity search test: {result.get('error', 'Unknown error')}")
            return False
        else:
            print("✅ Opportunity search test passed")
            return True
            
    except Exception as e:
        print(f"❌ Opportunity management test failed: {e}")
        return False

def test_real_estate_workflow():
    """Test the complete real estate lead workflow."""
    print("\n🏠 Testing real estate workflow...")
    
    try:
        client = GHLMCPClient()
        
        # Test data for a real estate lead
        contact_data = {
            "first_name": "John",
            "last_name": "Seller",
            "email": "<EMAIL>",
            "phone": "+1555123456",
            "source": "website",
            "tier": "1"
        }
        
        property_data = {
            "address": "456 Property Lane, Real City, RC 67890",
            "property_type": "single-family",
            "estimated_value": 250000.0,
            "pipeline_id": "test_pipeline_id"
        }
        
        # Test the complete workflow
        result = client.create_lead_workflow(contact_data, property_data)
        
        # Check if workflow components were called
        if isinstance(result, dict) and "contact" in result:
            print("✅ Real estate workflow test passed (workflow components called)")
            return True
        else:
            print("⚠️ Real estate workflow test: Partial success")
            return False
            
    except Exception as e:
        print(f"❌ Real estate workflow test failed: {e}")
        return False

def test_offer_notification():
    """Test offer notification system."""
    print("\n📧 Testing offer notification...")
    
    try:
        client = GHLMCPClient()
        
        # Test offer notification
        result = client.send_offer_notification(
            contact_id="test_contact_id",
            offer_amount=225000.0,
            property_address="456 Property Lane, Real City, RC 67890",
            offer_details={
                "closing_timeline": "30 days",
                "cash_offer": True,
                "inspection_period": "7 days"
            }
        )
        
        # Check if both SMS and email components were called
        if isinstance(result, dict) and "sms" in result and "email" in result:
            print("✅ Offer notification test passed (both SMS and email called)")
            return True
        else:
            print("⚠️ Offer notification test: Partial success")
            return False
            
    except Exception as e:
        print(f"❌ Offer notification test failed: {e}")
        return False

def test_mcp_tools_availability():
    """Test availability of key MCP tools."""
    print("\n🔧 Testing MCP tools availability...")
    
    try:
        import requests
        
        # Get available tools from MCP server
        response = requests.get("http://localhost:8000/tools", timeout=10)
        
        if response.status_code == 200:
            tools = response.json()
            tool_count = len(tools.get("tools", []))
            
            print(f"✅ MCP server reports {tool_count} available tools")
            
            # Check for key tool categories
            tool_names = [tool.get("name", "") for tool in tools.get("tools", [])]
            
            key_tools = [
                "create_contact",
                "send_sms", 
                "send_email",
                "create_opportunity",
                "search_contacts",
                "create_appointment"
            ]
            
            available_key_tools = [tool for tool in key_tools if tool in tool_names]
            
            print(f"✅ Key tools available: {len(available_key_tools)}/{len(key_tools)}")
            
            if len(available_key_tools) >= len(key_tools) * 0.8:  # 80% threshold
                return True
            else:
                print("⚠️ Some key tools may not be available")
                return False
        else:
            print(f"❌ Failed to get tools list: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Tools availability test failed: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 GoHighLevel MCP Integration Test Suite")
    print("=" * 60)
    
    # Start MCP server
    server_process = start_mcp_server()
    
    if not server_process:
        print("❌ Cannot start MCP server. Exiting.")
        sys.exit(1)
    
    try:
        # Run tests
        tests = [
            ("Basic Connectivity", test_basic_connectivity),
            ("MCP Tools Availability", test_mcp_tools_availability),
            ("Contact Management", test_contact_management),
            ("Messaging Tools", test_messaging_tools),
            ("Opportunity Management", test_opportunity_management),
            ("Real Estate Workflow", test_real_estate_workflow),
            ("Offer Notification", test_offer_notification)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n{'='*20} {test_name} {'='*20}")
            if test_func():
                passed += 1
        
        # Results
        print(f"\n{'='*60}")
        print(f"🎯 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! GoHighLevel MCP integration is ready.")
        elif passed >= total * 0.8:
            print("✅ Most tests passed. Integration is functional with minor issues.")
        else:
            print("⚠️ Several tests failed. Please check configuration and try again.")
        
        print("\n📋 Next Steps:")
        print("   1. Configure Claude Desktop with the MCP server")
        print("   2. Test real workflows with actual GHL data")
        print("   3. Deploy to production environment")
        
    finally:
        # Clean up
        print("\n🧹 Cleaning up...")
        server_process.terminate()
        server_process.wait()
        print("✅ MCP server stopped")

if __name__ == "__main__":
    main()
