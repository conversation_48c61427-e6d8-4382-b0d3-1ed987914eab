#!/usr/bin/env python3
"""
Integration script to connect the new GoHighLevel MCP with existing AI-OS system.
This script updates your existing agents to use the comprehensive MCP capabilities.
"""

import os
import sys
import json
import shutil
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def backup_existing_files():
    """Backup existing GHL-related files before integration."""
    print("📦 Creating backup of existing files...")
    
    backup_dir = Path("backups/ghl_integration_backup")
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    files_to_backup = [
        "agents/ghl_client.py",
        "config/ghl_config.json",
        "config/mcp_config.json"
    ]
    
    for file_path in files_to_backup:
        if Path(file_path).exists():
            backup_path = backup_dir / Path(file_path).name
            shutil.copy2(file_path, backup_path)
            print(f"✅ Backed up {file_path} to {backup_path}")
    
    print(f"✅ Backup completed in {backup_dir}")

def update_agent_imports():
    """Update existing agents to use the new MCP client."""
    print("🔄 Updating agent imports...")
    
    # Files that might import ghl_client
    agent_files = [
        "agents/offer_generator.py",
        "agents/offer_sender.py", 
        "agents/followup_bot.py",
        "agents/dispo_bot.py",
        "demo_enhanced_lead_processing.py"
    ]
    
    for file_path in agent_files:
        if Path(file_path).exists():
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                
                # Check if it imports ghl_client
                if "from agents.ghl_client import" in content or "import agents.ghl_client" in content:
                    print(f"📝 Found GHL imports in {file_path}")
                    
                    # Add comment about MCP upgrade
                    upgrade_comment = """
# NOTE: GoHighLevel MCP Integration Available
# The new GHLMCPClient provides 269+ tools for comprehensive automation.
# To upgrade this file:
# 1. Replace: from agents.ghl_client import GHLClient
# 2. With: from agents.ghl_mcp_client import GHLMCPClient
# 3. Update method calls to use enhanced MCP capabilities
# See docs/GHL_MCP_INTEGRATION.md for details
"""
                    
                    # Add the comment at the top after existing imports
                    lines = content.split('\n')
                    import_end = 0
                    for i, line in enumerate(lines):
                        if line.strip() and not line.startswith('#') and not line.startswith('import') and not line.startswith('from'):
                            import_end = i
                            break
                    
                    lines.insert(import_end, upgrade_comment)
                    
                    with open(file_path, 'w') as f:
                        f.write('\n'.join(lines))
                    
                    print(f"✅ Added upgrade notes to {file_path}")
                
            except Exception as e:
                print(f"⚠️ Could not update {file_path}: {e}")

def create_migration_examples():
    """Create example files showing how to migrate from old to new client."""
    print("📚 Creating migration examples...")
    
    examples_dir = Path("examples/ghl_mcp_migration")
    examples_dir.mkdir(parents=True, exist_ok=True)
    
    # Example 1: Basic contact creation migration
    basic_example = '''"""
Example: Migrating from basic GHL client to comprehensive MCP client
"""

# OLD WAY (basic ghl_client.py)
from agents.ghl_client import GHLClient

def old_create_contact_workflow(contact_data):
    client = GHLClient()
    
    # Basic contact creation
    result = client.send_sms(contact_data["id"], "Welcome message")
    return result

# NEW WAY (comprehensive MCP client with 269+ tools)
from agents.ghl_mcp_client import GHLMCPClient

def new_create_contact_workflow(contact_data, property_data):
    client = GHLMCPClient()
    
    # Complete workflow with multiple tools
    result = client.create_lead_workflow(contact_data, property_data)
    
    # Additional capabilities now available:
    # - Advanced contact management with custom fields
    # - Automated tagging and organization
    # - Task creation and assignment
    # - Workflow automation
    # - Multi-channel messaging (SMS + Email)
    # - Opportunity pipeline management
    # - Calendar integration
    # - And 260+ more tools!
    
    return result

# Example usage
if __name__ == "__main__":
    contact_data = {
        "first_name": "John",
        "last_name": "Seller",
        "email": "<EMAIL>",
        "phone": "+1555123456",
        "source": "website",
        "tier": "1"
    }
    
    property_data = {
        "address": "123 Main St",
        "property_type": "single-family",
        "estimated_value": 250000.0,
        "pipeline_id": "your_pipeline_id"
    }
    
    # Use the new comprehensive workflow
    result = new_create_contact_workflow(contact_data, property_data)
    print("Workflow result:", result)
'''
    
    with open(examples_dir / "basic_migration_example.py", 'w') as f:
        f.write(basic_example)
    
    # Example 2: Advanced real estate workflow
    advanced_example = '''"""
Example: Advanced real estate workflow using MCP tools
"""

from agents.ghl_mcp_client import GHLMCPClient
from datetime import datetime, timedelta

def comprehensive_lead_processing(lead_data):
    """
    Process a real estate lead using the full power of 269+ MCP tools.
    This replaces multiple separate functions with one comprehensive workflow.
    """
    client = GHLMCPClient()
    results = {}
    
    # 1. Create contact with advanced data
    contact_result = client.create_contact(
        first_name=lead_data["first_name"],
        last_name=lead_data["last_name"],
        email=lead_data["email"],
        phone=lead_data["phone"],
        address=lead_data["property_address"],
        custom_fields={
            "property_address": lead_data["property_address"],
            "lead_source": lead_data["source"],
            "tier": lead_data["tier"],
            "property_type": lead_data.get("property_type", ""),
            "estimated_value": lead_data.get("estimated_value", 0)
        }
    )
    results["contact_creation"] = contact_result
    
    if "error" not in contact_result:
        contact_id = contact_result["contact"]["id"]
        
        # 2. Advanced tagging based on lead characteristics
        tags = ["real-estate-lead", f"tier-{lead_data['tier']}"]
        if lead_data.get("property_type"):
            tags.append(f"property-{lead_data['property_type']}")
        if lead_data.get("source"):
            tags.append(f"source-{lead_data['source']}")
        
        results["tagging"] = client.add_contact_tags(contact_id, tags)
        
        # 3. Create opportunity in sales pipeline
        results["opportunity"] = client.create_opportunity(
            contact_id=contact_id,
            pipeline_id=lead_data.get("pipeline_id", "default_pipeline"),
            title=f"Property: {lead_data['property_address']}",
            value=lead_data.get("estimated_value", 0)
        )
        
        # 4. Send welcome message sequence
        welcome_sms = f"Hi {lead_data['first_name']}! Thanks for your interest in selling {lead_data['property_address']}. I'm analyzing your property value and will have more information soon."
        results["welcome_sms"] = client.send_sms(contact_id, welcome_sms)
        
        # 5. Create follow-up tasks
        results["analysis_task"] = client.create_contact_task(
            contact_id=contact_id,
            title="Property Analysis",
            description=f"Complete market analysis for {lead_data['property_address']}",
            due_date=(datetime.now() + timedelta(hours=2)).strftime("%Y-%m-%d")
        )
        
        results["followup_task"] = client.create_contact_task(
            contact_id=contact_id,
            title="Follow-up Call",
            description="Call to discuss property details and potential offer",
            due_date=(datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
        )
        
        # 6. Add to nurture workflow if tier 2 or 3
        if lead_data.get("tier") in ["2", "3"]:
            results["workflow"] = client.add_contact_to_workflow(
                contact_id=contact_id,
                workflow_id="nurture_sequence_workflow"
            )
    
    return results

def send_offer_with_full_automation(contact_id, offer_data):
    """
    Send an offer using comprehensive automation tools.
    """
    client = GHLMCPClient()
    
    # Send offer notification via multiple channels
    offer_result = client.send_offer_notification(
        contact_id=contact_id,
        offer_amount=offer_data["amount"],
        property_address=offer_data["property_address"],
        offer_details=offer_data.get("details", {})
    )
    
    # Create opportunity for offer tracking
    opportunity_result = client.create_opportunity(
        contact_id=contact_id,
        pipeline_id=offer_data.get("pipeline_id", "offers_pipeline"),
        title=f"Offer: ${offer_data['amount']:,.2f} - {offer_data['property_address']}",
        value=offer_data["amount"]
    )
    
    # Schedule follow-up appointment
    if offer_data.get("schedule_followup"):
        appointment_result = client.create_appointment(
            calendar_id=offer_data.get("calendar_id", "default_calendar"),
            contact_id=contact_id,
            title=f"Offer Discussion - {offer_data['property_address']}",
            start_time=offer_data.get("followup_time"),
            end_time=offer_data.get("followup_end_time"),
            description=f"Discuss offer of ${offer_data['amount']:,.2f} for {offer_data['property_address']}"
        )
    
    return {
        "offer_notification": offer_result,
        "opportunity": opportunity_result,
        "appointment": appointment_result if offer_data.get("schedule_followup") else None
    }

# Example usage
if __name__ == "__main__":
    # Process a comprehensive lead
    lead_data = {
        "first_name": "Sarah",
        "last_name": "Johnson", 
        "email": "<EMAIL>",
        "phone": "+1555987654",
        "property_address": "456 Oak Street, City, ST 12345",
        "source": "facebook_ad",
        "tier": "1",
        "property_type": "single-family",
        "estimated_value": 275000,
        "pipeline_id": "main_pipeline"
    }
    
    result = comprehensive_lead_processing(lead_data)
    print("Lead processing result:", result)
'''
    
    with open(examples_dir / "advanced_workflow_example.py", 'w') as f:
        f.write(advanced_example)
    
    print(f"✅ Migration examples created in {examples_dir}")

def create_integration_checklist():
    """Create a checklist for completing the integration."""
    print("📋 Creating integration checklist...")
    
    checklist = '''# GoHighLevel MCP Integration Checklist

## ✅ Setup Complete
- [x] GoHighLevel MCP server cloned and built
- [x] Environment variables configured
- [x] MCP configuration added to config/mcp_config.json
- [x] Enhanced GHL client created (agents/ghl_mcp_client.py)
- [x] Migration examples provided

## 🔄 Next Steps (Manual)

### 1. Test the Integration
```bash
# Run comprehensive test suite
python scripts/test_ghl_mcp_integration.py

# Test individual components
python scripts/setup_ghl_mcp.py
```

### 2. Configure Claude Desktop
- [ ] Copy claude-desktop-mcp-config.json contents to Claude Desktop mcp_settings.json
- [ ] Restart Claude Desktop
- [ ] Test with: "List available GoHighLevel tools"

### 3. Update Existing Agents (Choose Your Approach)

**Option A: Gradual Migration**
- [ ] Keep existing ghl_client.py for compatibility
- [ ] Use new GHLMCPClient for new features
- [ ] Gradually migrate existing code

**Option B: Full Migration**
- [ ] Replace ghl_client.py imports with ghl_mcp_client.py
- [ ] Update method calls to use enhanced capabilities
- [ ] Test all existing workflows

### 4. Enhanced Workflows
- [ ] Update lead processing to use create_lead_workflow()
- [ ] Enhance offer sending with send_offer_notification()
- [ ] Add advanced contact management with tagging
- [ ] Implement opportunity pipeline automation

### 5. Production Deployment
```bash
# Deploy MCP server
./scripts/deploy_ghl_mcp.sh

# Choose deployment method:
# - Local: ./start-ghl-mcp.sh
# - Docker: docker-compose -f docker-compose.ghl-mcp.yml up -d
# - Cloud: Deploy to Vercel/Railway/Render
```

### 6. Monitoring & Optimization
- [ ] Set up health monitoring: ./monitor-ghl-mcp.sh
- [ ] Monitor API usage and rate limits
- [ ] Optimize workflows based on performance data

## 🎯 Success Metrics
- [ ] 269+ tools available in Claude Desktop
- [ ] Real estate workflows automated end-to-end
- [ ] Lead processing time reduced by 80%+
- [ ] Contact management fully automated
- [ ] Offer notifications sent via multiple channels

## 📚 Resources
- docs/GHL_MCP_INTEGRATION.md - Complete documentation
- examples/ghl_mcp_migration/ - Migration examples
- scripts/test_ghl_mcp_integration.py - Testing suite
- https://github.com/mastanley13/GoHighLevel-MCP - Original repository

## 🆘 Support
If you encounter issues:
1. Check docs/GHL_MCP_INTEGRATION.md troubleshooting section
2. Run python scripts/test_ghl_mcp_integration.py for diagnostics
3. Verify environment variables and API credentials
4. Check MCP server logs for errors

## 🎉 You're Ready!
Once this checklist is complete, you'll have the most comprehensive GoHighLevel automation system available!
'''
    
    with open("GHL_MCP_INTEGRATION_CHECKLIST.md", 'w') as f:
        f.write(checklist)
    
    print("✅ Integration checklist created: GHL_MCP_INTEGRATION_CHECKLIST.md")

def main():
    """Main integration function."""
    print("🔗 GoHighLevel MCP Integration with Existing AI-OS System")
    print("=" * 70)
    
    # Backup existing files
    backup_existing_files()
    
    # Update agent imports with upgrade notes
    update_agent_imports()
    
    # Create migration examples
    create_migration_examples()
    
    # Create integration checklist
    create_integration_checklist()
    
    print("\n🎉 Integration Preparation Complete!")
    print("=" * 50)
    print("📋 Next Steps:")
    print("   1. Review GHL_MCP_INTEGRATION_CHECKLIST.md")
    print("   2. Run: python scripts/setup_ghl_mcp.py")
    print("   3. Test: python scripts/test_ghl_mcp_integration.py")
    print("   4. Configure Claude Desktop with generated config")
    print("   5. Start using 269+ GoHighLevel tools!")
    print("\n📚 Documentation: docs/GHL_MCP_INTEGRATION.md")
    print("🔧 Examples: examples/ghl_mcp_migration/")

if __name__ == "__main__":
    main()
