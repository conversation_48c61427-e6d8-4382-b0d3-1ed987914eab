
import os
import json
import logging
import time
from typing import Dict, Any, List, Optional, Union
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# GHL API constants
GHL_API_KEY = os.getenv("GHL_API_KEY")
GHL_BASE_URL = "https://rest.gohighlevel.com/v1" # May still be used for GET requests or non-n8n managed calls
GHL_BASE_URL = "https://rest.gohighlevel.com/v1"
GHL_LOCATION_ID = os.getenv("GHL_LOCATION_ID")  # For multi-location accounts

# n8n Webhook URLs (assuming n8n is accessible at 'http://n8n:5678' from within Docker network)
N8N_BASE_URL = os.getenv("N8N_INTERNAL_URL", "http://n8n:5678")
N8N_UPDATE_GHL_WEBHOOK_URL = f"{N8N_BASE_URL}/webhook/aios/update-ghl"
N8N_GHL_ALERT_WEBHOOK_URL = f"{N8N_BASE_URL}/webhook/util/ghl-alert"


class GHLClient:
    """
    Client for interacting with the GoHighLevel API, potentially via n8n workflows.
    Client for interacting with the GoHighLevel API.
    Handles notifications, contact management, and status tracking.
    """
    
    def __init__(self, api_key: Optional[str] = None, location_id: Optional[str] = None):
        """
        Initialize the GHL client.
        
        Args:
            api_key (Optional[str]): GHL API key. Defaults to environment variable.
            location_id (Optional[str]): GHL location ID. Defaults to environment variable.
        """
        self.api_key = api_key or GHL_API_KEY
        self.location_id = location_id or GHL_LOCATION_ID
        self.base_url = GHL_BASE_URL
        
        if not self.api_key:
            logger.warning("GHL API key not provided or found in environment variables")
        
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

    async def _call_n8n_webhook(self, url: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Helper method to call an n8n webhook.
        Using async for consistency if other methods become async, though requests is sync.
        If truly async operations are needed, httpx would be better.
        For now, this will run synchronously within an async method if called from one.
        """
        try:
            logger.info(f"Calling n8n webhook: {url} with payload: {payload}")
            # Using a longer timeout for n8n as it might do its own processing/retries
            response = requests.post(url, json=payload, timeout=30) 
            response.raise_for_status()
            # n8n webhooks typically respond with a simple success message if accepted
            # For example: {"message": "Workflow Succeeded"} or {"message": "Workflow Started"}
            # We'll return the JSON response or a success indicator.
            try:
                return response.json()
            except json.JSONDecodeError:
                # If n8n responds with non-JSON (e.g. simple string or 204 No Content)
                return {"status": "success", "statusCode": response.status_code, "data": response.text}
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to call n8n webhook {url}: {str(e)}")
            return {"error": str(e), "success": False}
    
    def send_sms(self, contact_id: str, body: str) -> Dict[str, Any]:
        """
        Send an SMS via GHL to a contact.
        
        Args:
            contact_id (str): GHL contact ID
            body (str): Message body
            
        Returns:
            Dict[str, Any]: API response
        """
        url = f"{self.base_url}/conversations/sms"
        payload = {
            "contactId": contact_id,
            "body": body
        }
        
        try:
            logger.info(f"Sending SMS to contact {contact_id}")
            response = requests.post(url, json=payload, headers=self.headers, timeout=10)
            response.raise_for_status()
            logger.info(f"SMS sent successfully to contact {contact_id}")
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to send SMS: {str(e)}")
            return {"error": str(e), "success": False}
    
    def send_email(self, contact_id: str, subject: str, body: str) -> Dict[str, Any]:
        """
        Send an email via GHL to a contact.
        
        Args:
            contact_id (str): GHL contact ID
            subject (str): Email subject
            body (str): Email body (HTML supported)
            
        Returns:
            Dict[str, Any]: API response
        """
        url = f"{self.base_url}/conversations/email"
        payload = {
            "contactId": contact_id,
            "subject": subject,
            "body": body
        }
        
        try:
            logger.info(f"Sending email to contact {contact_id}")
            response = requests.post(url, json=payload, headers=self.headers, timeout=10)
            response.raise_for_status()
            logger.info(f"Email sent successfully to contact {contact_id}")
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to send email: {str(e)}")
            return {"error": str(e), "success": False}
    
    def create_task(self, contact_id: str, title: str, description: str, 
                  due_date: str, assignee_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a task in GHL for a contact.
        
        Args:
            contact_id (str): GHL contact ID
            title (str): Task title
            description (str): Task description
            due_date (str): Due date in YYYY-MM-DD format
            assignee_id (Optional[str]): User ID to assign the task to
            
        Returns:
            Dict[str, Any]: API response
        """
        url = f"{self.base_url}/tasks"
        
        payload = {
            "contactId": contact_id,
            "title": title,
            "description": description,
            "dueDate": due_date
        }
        
        if assignee_id:
            payload["assigneeId"] = assignee_id
        
        try:
            logger.info(f"Creating task for contact {contact_id}")
            response = requests.post(url, json=payload, headers=self.headers, timeout=10)
            response.raise_for_status()
            logger.info(f"Task created successfully for contact {contact_id}")
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to create task: {str(e)}")
            return {"error": str(e), "success": False}
    
    def add_note(self, contact_id: str, note: str) -> Dict[str, Any]:
        """
        Add a note to a contact in GHL.
        
        Args:
            contact_id (str): GHL contact ID
            note (str): Note content
            
        Returns:
            Dict[str, Any]: API response
        """
        url = f"{self.base_url}/contacts/{contact_id}/notes"
        payload = {
            "note": note
        }
        
        try:
            logger.info(f"Adding note to contact {contact_id}")
            response = requests.post(url, json=payload, headers=self.headers, timeout=10)
            response.raise_for_status()
            logger.info(f"Note added successfully to contact {contact_id}")
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to add note: {str(e)}")
            return {"error": str(e), "success": False}
    
    def update_contact_custom_field(self, contact_id: str, field_name: str, 
                                  field_value: Union[str, int, bool]) -> Dict[str, Any]:
        """
        Update a custom field for a contact in GHL.
        
        Args:
            contact_id (str): GHL contact ID
            field_name (str): Custom field name
            field_value (Union[str, int, bool]): Custom field value
            
        Returns:
            Dict[str, Any]: API response
        """
        url = f"{self.base_url}/contacts/{contact_id}"
        
        # First get current contact data
        try:
            get_response = requests.get(url, headers=self.headers, timeout=10)
            get_response.raise_for_status()
            contact_data = get_response.json()
            
            # Update custom fields
            custom_fields = contact_data.get("customFields", {})
            custom_fields[field_name] = field_value
            
            # Create update payload with only the custom fields
            payload = {
                "customFields": custom_fields
            }
            
            logger.info(f"Updating custom field {field_name} for contact {contact_id}")
            update_response = requests.put(url, json=payload, headers=self.headers, timeout=10)
            update_response.raise_for_status()
            logger.info(f"Custom field updated successfully for contact {contact_id}")
            return update_response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to update custom field: {str(e)}")
            return {"error": str(e), "success": False}
    
    def get_contact(self, contact_id: str) -> Dict[str, Any]:
        """
        Get contact details from GHL.
        
        Args:
            contact_id (str): GHL contact ID
            
        Returns:
            Dict[str, Any]: Contact details
        """
        url = f"{self.base_url}/contacts/{contact_id}"
        
        try:
            logger.info(f"Fetching contact {contact_id}")
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get contact: {str(e)}")
            return {"error": str(e), "success": False}
    
    def search_contacts(self, query: str) -> List[Dict[str, Any]]:
        """
        Search for contacts in GHL based on a query.
        
        Args:
            query (str): Search query
            
        Returns:
            List[Dict[str, Any]]: List of contacts
        """
        url = f"{self.base_url}/contacts/search"
        payload = {
            "query": query
        }
        
        try:
            logger.info(f"Searching contacts with query: {query}")
            response = requests.post(url, json=payload, headers=self.headers, timeout=10)
            response.raise_for_status()
            contacts = response.json().get("contacts", [])
            logger.info(f"Found {len(contacts)} contacts")
            return contacts
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to search contacts: {str(e)}")
            return []
    
    def create_opportunity(self, contact_id: str, pipeline_id: str, 
                         stage_id: str, title: str, value: float = 0.0, 
                         custom_fields: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create an opportunity in GHL.
        
        Args:
            contact_id (str): GHL contact ID
            pipeline_id (str): Pipeline ID
            stage_id (str): Stage ID
            title (str): Opportunity title
            value (float): Opportunity value
            custom_fields (Optional[Dict[str, Any]]): Custom fields
            
        Returns:
            Dict[str, Any]: API response
        """
        url = f"{self.base_url}/pipelines/{pipeline_id}/opportunities"
        
        payload = {
            "contactId": contact_id,
            "stageId": stage_id,
            "name": title,
            "value": value
        }
        
        if custom_fields:
            payload["customFields"] = custom_fields
        
        try:
            logger.info(f"Creating opportunity for contact {contact_id}")
            response = requests.post(url, json=payload, headers=self.headers, timeout=10)
            response.raise_for_status()
            logger.info(f"Opportunity created successfully for contact {contact_id}")
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to create opportunity: {str(e)}")
            return {"error": str(e), "success": False}
    
    def update_opportunity_stage(self, pipeline_id: str, opportunity_id: str, 
                               stage_id: str) -> Dict[str, Any]:
        """
        Update the stage of an opportunity in GHL.
        
        Args:
            pipeline_id (str): Pipeline ID
            opportunity_id (str): Opportunity ID
            stage_id (str): New stage ID
            
        Returns:
            Dict[str, Any]: API response
        """
        url = f"{self.base_url}/pipelines/{pipeline_id}/opportunities/{opportunity_id}"
        
        payload = {
            "stageId": stage_id
        }
        
        try:
            logger.info(f"Updating stage for opportunity {opportunity_id}")
            response = requests.put(url, json=payload, headers=self.headers, timeout=10)
            response.raise_for_status()
            logger.info(f"Opportunity stage updated successfully for opportunity {opportunity_id}")
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to update opportunity stage: {str(e)}")
            return {"error": str(e), "success": False}
    
    def get_opportunity(self, pipeline_id: str, opportunity_id: str) -> Dict[str, Any]:
        """
        Get opportunity details from GHL.
        
        Args:
            pipeline_id (str): Pipeline ID
            opportunity_id (str): Opportunity ID
            
        Returns:
            Dict[str, Any]: Opportunity details
        """
        url = f"{self.base_url}/pipelines/{pipeline_id}/opportunities/{opportunity_id}"
        
        try:
            logger.info(f"Fetching opportunity {opportunity_id}")
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get opportunity: {str(e)}")
            return {"error": str(e), "success": False}
    
    def get_opportunity_custom_field(self, pipeline_id: str, opportunity_id: str, 
                                   field_name: str) -> Any:
        """
        Get a custom field value from an opportunity in GHL.
        
        Args:
            pipeline_id (str): Pipeline ID
            opportunity_id (str): Opportunity ID
            field_name (str): Custom field name
            
        Returns:
            Any: Custom field value
        """
        opportunity = self.get_opportunity(pipeline_id, opportunity_id)
        
        if "error" in opportunity:
            return None
        
        return opportunity.get("customFields", {}).get(field_name)
    
    def wait_for_opportunity_stage_change(self, pipeline_id: str, opportunity_id: str, 
                                        current_stage_id: str, target_stage_ids: List[str], 
                                        timeout_seconds: int = 3600, poll_interval: int = 60) -> Dict[str, Any]:
        """
        Wait for an opportunity to change to one of the target stages.
        
        Args:
            pipeline_id (str): Pipeline ID
            opportunity_id (str): Opportunity ID
            current_stage_id (str): Current stage ID
            target_stage_ids (List[str]): List of target stage IDs to wait for
            timeout_seconds (int): Maximum time to wait in seconds
            poll_interval (int): Time between checks in seconds
            
        Returns:
            Dict[str, Any]: Final opportunity data or error
        """
        start_time = time.time()
        
        logger.info(f"Waiting for opportunity {opportunity_id} to change stage from {current_stage_id} to one of {target_stage_ids}")
        
        while time.time() - start_time < timeout_seconds:
            opportunity = self.get_opportunity(pipeline_id, opportunity_id)
            
            if "error" in opportunity:
                return opportunity
            
            current_stage = opportunity.get("stageId")
            
            if current_stage in target_stage_ids:
                logger.info(f"Opportunity {opportunity_id} changed to target stage {current_stage}")
                return opportunity
            
            # Sleep before checking again
            time.sleep(poll_interval)
        
        logger.warning(f"Timeout waiting for opportunity {opportunity_id} to change stage")
        return {"error": "Timeout waiting for stage change", "success": False}


# Legacy function to maintain backward compatibility
def send_sms(contact_id: str, body: str) -> bool:
    """
    Send an SMS via GHL to a contact (legacy function).
    
    Args:
        contact_id (str): GHL contact ID
        body (str): Message body
        
    Returns:
        bool: True if successful, False otherwise
    """
    client = GHLClient()
    result = client.send_sms(contact_id, body)
    return "error" not in result
    