{"api": {"base_url": "https://rest.gohighlevel.com/v1", "use_location_id": true}, "pipelines": {"approval": {"id": "approval_pipeline_id", "stages": {"submitted": "submitted_stage_id", "approved": "approved_stage_id", "rejected": "rejected_stage_id"}}, "lead": {"id": "lead_pipeline_id", "stages": {"new": "new_lead_stage_id", "in_progress": "in_progress_stage_id", "qualified": "qualified_stage_id", "offer_sent": "offer_sent_stage_id", "negotiating": "negotiating_stage_id", "contract": "contract_stage_id", "closed": "closed_stage_id", "lost": "lost_stage_id"}}}, "templates": {"approval_request": ["Hi {first_name}, I need your approval for the {property_address} deal. We're offering ${offer_amount}. Please let me know if this is approved.", "Hello {first_name}! Please review and approve the offer for {property_address} at ${offer_amount}. Thanks!"], "approval_granted": ["Great! Your approval for {property_address} has been received. We'll proceed with the offer.", "Thanks for approving the {property_address} deal. We're moving forward with the next steps."], "approval_rejected": ["Noted. Your rejection of the {property_address} deal has been recorded. We won't proceed with this offer.", "I've received your rejection for the {property_address} offer. Let me know if you'd like to discuss alternatives."], "status_in_progress": ["Just to let you know, we're actively working on the {property_address} deal. Current status: In Progress.", "Update on {property_address}: We're making progress and will keep you informed of any developments."], "status_completed": ["Good news! The {property_address} deal has been completed successfully.", "Just completed the {property_address} deal. Everything went according to plan."], "status_error": ["We've encountered an issue with the {property_address} deal. Please check the dashboard for details.", "There's a problem with the {property_address} transaction that needs your attention. Please review."], "status_waiting": ["The {property_address} deal is currently on hold, waiting for {waiting_reason}.", "Just to update you: we're waiting on {waiting_reason} for the {property_address} property."], "critical_event": ["CRITICAL ALERT: {event_type} for {property_address}. Action required immediately.", "URGENT: {event_type} situation with {property_address}. Please respond ASAP."], "lead_welcome": ["Hi {first_name}, thanks for your interest in selling {property_address}. I'm analyzing your property value and will have more information soon.", "Hello {first_name}! I've received your information about {property_address} and am working on a detailed analysis for you."], "offer_notification": ["Hi {first_name}, we'd like to make an offer of ${offer_amount} for your property at {property_address}. Let me know if you'd like to discuss.", "Hello {first_name}! Based on our analysis, we can offer ${offer_amount} for {property_address}. Interested in discussing further?"]}, "custom_fields": {"dnc": "Do Not Contact", "offer_amount": "Offer Amount", "tier": "Lead Tier", "source": "Lead Source", "notification_preference": "Notification Preference"}, "webhook": {"lead": "/webhook/lead", "opportunity": "/webhook/opportunity", "contact": "/webhook/contact"}}