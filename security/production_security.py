"""
Production Security Configuration for AI-OS
Implements security hardening, rate limiting, and access controls
"""

import os
import hashlib
import hmac
import time
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from functools import wraps
from flask import request, jsonify, abort
import jwt
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

class SecurityConfig:
    """Security configuration settings"""
    
    # Rate limiting
    RATE_LIMIT_REQUESTS = int(os.getenv("RATE_LIMIT_REQUESTS", "100"))
    RATE_LIMIT_WINDOW = int(os.getenv("RATE_LIMIT_WINDOW", "3600"))  # 1 hour
    
    # API Security
    API_KEY_HEADER = "X-API-Key"
    WEBHOOK_SECRET_HEADER = "X-Webhook-Signature"
    
    # JWT Settings
    JWT_SECRET = os.getenv("JWT_SECRET", "change-me-in-production")
    JWT_EXPIRY_HOURS = int(os.getenv("JWT_EXPIRY_HOURS", "24"))
    
    # IP Whitelist
    ALLOWED_IPS = os.getenv("ALLOWED_IPS", "").split(",") if os.getenv("ALLOWED_IPS") else []
    
    # Security Headers
    SECURITY_HEADERS = {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
        "Content-Security-Policy": "default-src 'self'",
        "Referrer-Policy": "strict-origin-when-cross-origin"
    }

class RateLimiter:
    """Rate limiting implementation"""
    
    def __init__(self):
        self.requests = defaultdict(deque)
        self.blocked_ips = {}
    
    def is_allowed(self, identifier: str, limit: int = None, window: int = None) -> bool:
        """Check if request is allowed under rate limit"""
        limit = limit or SecurityConfig.RATE_LIMIT_REQUESTS
        window = window or SecurityConfig.RATE_LIMIT_WINDOW
        
        now = time.time()
        
        # Check if IP is temporarily blocked
        if identifier in self.blocked_ips:
            if now < self.blocked_ips[identifier]:
                return False
            else:
                del self.blocked_ips[identifier]
        
        # Clean old requests
        request_times = self.requests[identifier]
        while request_times and request_times[0] < now - window:
            request_times.popleft()
        
        # Check rate limit
        if len(request_times) >= limit:
            # Block IP for 1 hour if rate limit exceeded
            self.blocked_ips[identifier] = now + 3600
            logger.warning(f"Rate limit exceeded for {identifier}, blocking for 1 hour")
            return False
        
        # Add current request
        request_times.append(now)
        return True
    
    def get_stats(self, identifier: str) -> Dict[str, Any]:
        """Get rate limiting stats for identifier"""
        now = time.time()
        window = SecurityConfig.RATE_LIMIT_WINDOW
        
        request_times = self.requests[identifier]
        recent_requests = [t for t in request_times if t > now - window]
        
        return {
            "requests_in_window": len(recent_requests),
            "limit": SecurityConfig.RATE_LIMIT_REQUESTS,
            "window_seconds": window,
            "blocked": identifier in self.blocked_ips,
            "block_expires": self.blocked_ips.get(identifier, 0)
        }

class SecurityManager:
    """Main security manager"""
    
    def __init__(self):
        self.rate_limiter = RateLimiter()
        self.valid_api_keys = self._load_api_keys()
        self.webhook_secrets = self._load_webhook_secrets()
    
    def _load_api_keys(self) -> Dict[str, Dict[str, Any]]:
        """Load valid API keys and their permissions"""
        api_keys = {}
        
        # Main API key
        main_api_key = os.getenv("API_KEY")
        if main_api_key:
            api_keys[main_api_key] = {
                "name": "main_api",
                "permissions": ["read", "write", "admin"],
                "rate_limit": 1000
            }
        
        # Retool API key
        retool_api_key = os.getenv("RETOOL_API_KEY")
        if retool_api_key:
            api_keys[retool_api_key] = {
                "name": "retool",
                "permissions": ["read", "write"],
                "rate_limit": 500
            }
        
        # Read-only API key
        readonly_api_key = os.getenv("READONLY_API_KEY")
        if readonly_api_key:
            api_keys[readonly_api_key] = {
                "name": "readonly",
                "permissions": ["read"],
                "rate_limit": 200
            }
        
        return api_keys
    
    def _load_webhook_secrets(self) -> Dict[str, str]:
        """Load webhook secrets for signature verification"""
        return {
            "ghl": os.getenv("GHL_WEBHOOK_SECRET", ""),
            "general": os.getenv("WEBHOOK_SECRET", "")
        }
    
    def verify_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """Verify API key and return permissions"""
        return self.valid_api_keys.get(api_key)
    
    def verify_webhook_signature(self, payload: bytes, signature: str, webhook_type: str = "general") -> bool:
        """Verify webhook signature"""
        secret = self.webhook_secrets.get(webhook_type)
        if not secret:
            logger.warning(f"No webhook secret configured for type: {webhook_type}")
            return False
        
        # Calculate expected signature
        expected_signature = hmac.new(
            secret.encode('utf-8'),
            payload,
            hashlib.sha256
        ).hexdigest()
        
        # Compare signatures
        return hmac.compare_digest(f"sha256={expected_signature}", signature)
    
    def check_ip_whitelist(self, ip: str) -> bool:
        """Check if IP is in whitelist (if configured)"""
        if not SecurityConfig.ALLOWED_IPS or not SecurityConfig.ALLOWED_IPS[0]:
            return True  # No whitelist configured
        
        return ip in SecurityConfig.ALLOWED_IPS
    
    def generate_jwt_token(self, user_id: str, permissions: List[str]) -> str:
        """Generate JWT token"""
        payload = {
            "user_id": user_id,
            "permissions": permissions,
            "exp": datetime.utcnow() + timedelta(hours=SecurityConfig.JWT_EXPIRY_HOURS),
            "iat": datetime.utcnow()
        }
        
        return jwt.encode(payload, SecurityConfig.JWT_SECRET, algorithm="HS256")
    
    def verify_jwt_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify JWT token"""
        try:
            payload = jwt.decode(token, SecurityConfig.JWT_SECRET, algorithms=["HS256"])
            return payload
        except jwt.ExpiredSignatureError:
            logger.warning("JWT token expired")
            return None
        except jwt.InvalidTokenError:
            logger.warning("Invalid JWT token")
            return None

# Global security manager
security_manager = SecurityManager()

def require_api_key(permissions: List[str] = None):
    """Decorator to require valid API key"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            api_key = request.headers.get(SecurityConfig.API_KEY_HEADER)
            
            if not api_key:
                abort(401, description="API key required")
            
            key_info = security_manager.verify_api_key(api_key)
            if not key_info:
                abort(401, description="Invalid API key")
            
            # Check permissions
            if permissions:
                user_permissions = key_info.get("permissions", [])
                if not any(perm in user_permissions for perm in permissions):
                    abort(403, description="Insufficient permissions")
            
            # Check rate limit
            client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            identifier = f"{api_key}:{client_ip}"
            
            if not security_manager.rate_limiter.is_allowed(identifier, key_info.get("rate_limit")):
                abort(429, description="Rate limit exceeded")
            
            # Add key info to request context
            request.api_key_info = key_info
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_webhook_signature(webhook_type: str = "general"):
    """Decorator to require valid webhook signature"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            signature = request.headers.get(SecurityConfig.WEBHOOK_SECRET_HEADER)
            
            if not signature:
                abort(401, description="Webhook signature required")
            
            payload = request.get_data()
            
            if not security_manager.verify_webhook_signature(payload, signature, webhook_type):
                abort(401, description="Invalid webhook signature")
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_ip_whitelist():
    """Decorator to require IP whitelist"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            
            if not security_manager.check_ip_whitelist(client_ip):
                logger.warning(f"Access denied for IP: {client_ip}")
                abort(403, description="Access denied")
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def add_security_headers():
    """Add security headers to response"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            response = f(*args, **kwargs)
            
            # Add security headers
            for header, value in SecurityConfig.SECURITY_HEADERS.items():
                response.headers[header] = value
            
            return response
        return decorated_function
    return decorator

def rate_limit(requests_per_hour: int = None):
    """Decorator for custom rate limiting"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            
            if not security_manager.rate_limiter.is_allowed(client_ip, requests_per_hour):
                abort(429, description="Rate limit exceeded")
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def log_security_event(event_type: str, details: Dict[str, Any]):
    """Log security events for monitoring"""
    security_event = {
        "timestamp": datetime.utcnow().isoformat(),
        "event_type": event_type,
        "client_ip": request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr),
        "user_agent": request.headers.get('User-Agent', ''),
        "endpoint": request.endpoint,
        "method": request.method,
        "details": details
    }
    
    logger.warning(f"Security Event: {event_type}", extra=security_event)

def validate_input_data(data: Dict[str, Any], required_fields: List[str] = None) -> bool:
    """Validate input data for security"""
    if required_fields:
        for field in required_fields:
            if field not in data:
                return False
    
    # Check for potential injection attacks
    dangerous_patterns = [
        "<script", "javascript:", "onload=", "onerror=",
        "DROP TABLE", "DELETE FROM", "INSERT INTO",
        "../", "..\\", "/etc/passwd"
    ]
    
    def check_value(value):
        if isinstance(value, str):
            value_lower = value.lower()
            for pattern in dangerous_patterns:
                if pattern.lower() in value_lower:
                    return False
        elif isinstance(value, dict):
            for v in value.values():
                if not check_value(v):
                    return False
        elif isinstance(value, list):
            for item in value:
                if not check_value(item):
                    return False
        return True
    
    return check_value(data)

# Security middleware for Flask app
def init_security_middleware(app):
    """Initialize security middleware for Flask app"""
    
    @app.before_request
    def security_checks():
        # Check IP whitelist for sensitive endpoints
        if request.endpoint in ['admin', 'config', 'system']:
            client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            if not security_manager.check_ip_whitelist(client_ip):
                log_security_event("ip_blocked", {"ip": client_ip, "endpoint": request.endpoint})
                abort(403)
        
        # Log suspicious requests
        user_agent = request.headers.get('User-Agent', '').lower()
        suspicious_agents = ['sqlmap', 'nikto', 'nmap', 'masscan']
        if any(agent in user_agent for agent in suspicious_agents):
            log_security_event("suspicious_user_agent", {"user_agent": user_agent})
    
    @app.after_request
    def add_security_headers_middleware(response):
        # Add security headers to all responses
        for header, value in SecurityConfig.SECURITY_HEADERS.items():
            response.headers[header] = value
        return response
    
    @app.errorhandler(429)
    def rate_limit_handler(e):
        return jsonify({
            "error": "Rate limit exceeded",
            "message": "Too many requests. Please try again later."
        }), 429
    
    @app.errorhandler(401)
    def unauthorized_handler(e):
        log_security_event("unauthorized_access", {"error": str(e)})
        return jsonify({
            "error": "Unauthorized",
            "message": "Valid authentication required"
        }), 401
    
    @app.errorhandler(403)
    def forbidden_handler(e):
        log_security_event("forbidden_access", {"error": str(e)})
        return jsonify({
            "error": "Forbidden",
            "message": "Access denied"
        }), 403
