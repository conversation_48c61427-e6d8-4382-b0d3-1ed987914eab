version: '3.8'

services:
  # PostgreSQL database for Retool
  postgres:
    image: postgres:15
    container_name: ai-os-postgres
    environment:
      POSTGRES_DB: retool
      POSTGRES_USER: retool
      POSTGRES_PASSWORD: retoolpassword
    volumes:
      - postgres-data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    networks:
      - aios_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U retool -d retool"]
      interval: 10s
      timeout: 5s
      retries: 5



  # AI-OS API service with integrated automation
  api:
    build: .
    container_name: ai-os-api
    ports:
      - "5002:5002"
    environment:
      - FLASK_APP=api/app.py
      - FLASK_ENV=production
      - API_HOST=0.0.0.0
      - API_PORT=5002
      - PYTHONPATH=/app
      - PORT=5002
      - DATABASE_URL=${DATABASE_URL}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - GHL_API_KEY=${GHL_API_KEY}
      - GHL_LOCATION_ID=${GHL_LOCATION_ID}
      - GHL_LOCATION_KEY=${GHL_LOCATION_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - RETOOL_API_KEY=${RETOOL_API_KEY}
      - RETOOL_ORG_ID=${RETOOL_ORG_ID}
      - WEBHOOK_SECRET=${WEBHOOK_SECRET}
      - WEBHOOK_URL=${WEBHOOK_URL:-http://localhost:5002}
    volumes:
      - .:/app
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - aios_network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5002/api/v1/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    command: ["python", "api/app.py"]

  # Retool self-hosted dashboard
  retool:
    image: tryretool/backend:latest
    container_name: ai-os-retool
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - SERVICE_TYPE=MAIN_BACKEND,DB_CONNECTOR,DB_SSH_CONNECTOR
      - FORCE_DEPLOYMENT=true
      - POSTGRES_DB=retool
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_USER=retool
      - POSTGRES_PASSWORD=retoolpassword
      - POSTGRES_SSL_ENABLED=false
      - JWT_SECRET=${RETOOL_JWT_SECRET:-retool-jwt-secret-change-me}
      - ENCRYPTION_KEY=${RETOOL_ENCRYPTION_KEY:-retool-encryption-key-change-me}
      - LICENSE_KEY=${RETOOL_LICENSE_KEY:-}
      - RETOOL_HOSTNAME=${RETOOL_HOSTNAME:-http://localhost:3000}
      # Internal auth settings
      - DISABLE_SIGNUPS=false
      - RESTRICTED_DOMAIN=${RETOOL_RESTRICTED_DOMAIN:-}
      # API settings
      - API_ENABLED=true
      - API_KEY=${RETOOL_API_KEY}
      # Database connections for the dashboard
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - PG_CONN_STRING_READONLY=${PG_CONN_STRING_READONLY}
      - GHL_API_KEY=${GHL_API_KEY}
    volumes:
      - retool-data:/data
      - ./retool:/retool-config
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - aios_network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/checkHealth || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: unless-stopped

volumes:
  postgres-data:
    driver: local
  retool-data:
    driver: local

networks:
  aios_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
