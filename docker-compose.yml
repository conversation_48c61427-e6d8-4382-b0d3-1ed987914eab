version: '3.8'

services:
  # PostgreSQL database for n8n and Retool
  postgres:
    image: postgres:15
    container_name: ai-os-postgres
    environment:
      POSTGRES_DB: n8n
      POSTGRES_USER: n8n
      POSTGRES_PASSWORD: n8npassword
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init-retool-db.sql:/docker-entrypoint-initdb.d/init-retool-db.sql
    ports:
      - "5433:5432"
    networks:
      - aios_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U n8n -d n8n"]
      interval: 10s
      timeout: 5s
      retries: 5

  # n8n workflow automation
  n8n:
    image: n8nio/n8n:latest
    container_name: ai-os-n8n
    ports:
      - "5678:5678"
    env_file:
      - .env
    environment:
      DB_TYPE: "postgresdb"
      DB_POSTGRESDB_HOST: "postgres"
      DB_POSTGRESDB_PORT: "5432"
      DB_POSTGRESDB_DATABASE: "n8n"
      DB_POSTGRESDB_USER: "n8n"
      DB_POSTGRESDB_PASSWORD: "n8npassword"
      N8N_HOST: "0.0.0.0"
      N8N_PORT: "5678"
      N8N_PROTOCOL: "http"
      WEBHOOK_URL: "${WEBHOOK_URL:-http://localhost:5678}"
      GENERIC_TIMEZONE: "UTC"
      N8N_LOG_LEVEL: "info"
      N8N_METRICS: "true"
      N8N_API_KEY: "${N8N_API_KEY}"
      N8N_DISABLE_UI: "false"
      N8N_BASIC_AUTH_ACTIVE: "false"
      N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN: "true"
      EXECUTIONS_DATA_PRUNE: "true"
      EXECUTIONS_DATA_MAX_AGE: "168"
      N8N_PAYLOAD_SIZE_MAX: "16"
      # Supabase connection for workflows
      SUPABASE_URL: "${SUPABASE_URL}"
      SUPABASE_KEY: "${SUPABASE_KEY}"
      SUPABASE_SERVICE_KEY: "${SUPABASE_SERVICE_KEY}"
    volumes:
      - n8n-data:/home/<USER>/.n8n
      - ./n8n/workflows:/home/<USER>/.n8n/workflows
      - ./ghl-lead-workflow.json:/home/<USER>/.n8n/workflows/ghl-lead-workflow.json
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - aios_network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:5678/healthz || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Enhanced Lead Processing API service
  api:
    build: .
    container_name: ai-os-api
    ports:
      - "5002:5002"
    environment:
      - FLASK_APP=api/app.py
      - FLASK_ENV=production
      - API_HOST=0.0.0.0
      - API_PORT=5002
      - PYTHONPATH=/app
      - PORT=5002
      - DATABASE_URL=${DATABASE_URL}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - N8N_WEBHOOK_URL=http://n8n:5678/webhook/ghl/lead
      - N8N_ALERT_WEBHOOK_URL=http://n8n:5678/webhook-test/util/ghl-alert
      - GHL_API_KEY=${GHL_API_KEY}
      - GHL_LOCATION_ID=${GHL_LOCATION_ID}
      - GHL_LOCATION_KEY=${GHL_LOCATION_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - RETOOL_API_KEY=${RETOOL_API_KEY}
      - RETOOL_ORG_ID=${RETOOL_ORG_ID}
      - WEBHOOK_SECRET=${WEBHOOK_SECRET}
    volumes:
      - .:/app
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      n8n:
        condition: service_healthy
    networks:
      - aios_network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5002/api/v1/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    command: ["python", "api/app.py"]

  # Retool self-hosted dashboard
  retool:
    image: tryretool/backend:latest
    container_name: ai-os-retool
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - SERVICE_TYPE=MAIN_BACKEND,DB_CONNECTOR,DB_SSH_CONNECTOR
      - FORCE_DEPLOYMENT=true
      - POSTGRES_DB=retool
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_USER=retool
      - POSTGRES_PASSWORD=retoolpassword
      - POSTGRES_SSL_ENABLED=false
      - JWT_SECRET=${RETOOL_JWT_SECRET:-retool-jwt-secret-change-me}
      - ENCRYPTION_KEY=${RETOOL_ENCRYPTION_KEY:-retool-encryption-key-change-me}
      - LICENSE_KEY=${RETOOL_LICENSE_KEY:-}
      - RETOOL_HOSTNAME=${RETOOL_HOSTNAME:-http://localhost:3000}
      # Internal auth settings
      - DISABLE_SIGNUPS=false
      - RESTRICTED_DOMAIN=${RETOOL_RESTRICTED_DOMAIN:-}
      # API settings
      - API_ENABLED=true
      - API_KEY=${RETOOL_API_KEY}
      # Database connections for the dashboard
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - PG_CONN_STRING_READONLY=${PG_CONN_STRING_READONLY}
      - GHL_API_KEY=${GHL_API_KEY}
    volumes:
      - retool-data:/data
      - ./retool:/retool-config
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - aios_network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/checkHealth || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: unless-stopped

volumes:
  postgres-data:
    driver: local
  n8n-data:
    driver: local
  retool-data:
    driver: local

networks:
  aios_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
