# GoHighLevel MCP Server - Test Commands

## 🎉 Congratulations! Your GoHighLevel MCP Server is configured!

Your <PERSON> should now have access to **253 GoHighLevel tools**. Here are some test commands to verify everything is working:

## 🧪 Basic Connection Tests

### 1. Test Contact Search
```
Search for contacts with the name "audrey" in GoHighLevel
```

### 2. List All Contacts
```
Get the first 5 contacts from my GoHighLevel account
```

### 3. Get Location Information
```
Show me information about my GoHighLevel location
```

## 🎯 Real Estate Specific Tests

### 4. Search for Tier 1 Leads
```
Find all contacts tagged with "tier 1" in GoHighLevel
```

### 5. Create a New Lead
```
Create a new contact in GoHighLevel with:
- Name: Test Lead
- Phone: +**********
- Email: <EMAIL>
- Tags: tier 2
```

### 6. Send SMS to a Contact
```
Send an SMS message to the contact "audrey plucinski" saying "Hi <PERSON>, following up on our conversation about the property. Any updates?"
```

## 💰 Opportunity Management Tests

### 7. List Sales Pipelines
```
Show me all the sales pipelines in my GoHighLevel account
```

### 8. Search for Opportunities
```
Find all opportunities in my GoHighLevel account
```

### 9. Create a New Opportunity
```
Create a new opportunity for "audrey plucinski" with:
- Title: "123 Main St Property Deal"
- Value: $50000
- Stage: Initial Contact
```

## 🗓️ Calendar Tests

### 10. List Calendars
```
Show me all calendars in my GoHighLevel account
```

### 11. Check Available Slots
```
Check available appointment slots for tomorrow between 9 AM and 5 PM
```

## 📧 Communication Tests

### 12. Send Email
```
Send an email to "audrey plucinski" with subject "Property Follow-up" and message "Hi Audrey, I wanted to follow up on the property we discussed. Please let me know if you have any questions."
```

### 13. Get Conversations
```
Show me recent conversations with "audrey plucinski"
```

## 🏢 Advanced Tests

### 14. Get Custom Fields
```
Show me all custom fields configured in my GoHighLevel location
```

### 15. List Tags
```
Show me all tags available in my GoHighLevel account
```

## 🔍 Troubleshooting

If any commands don't work:

1. **Check MCP Server Status**: Make sure the MCP server is still running in the terminal
2. **Restart Claude Desktop**: Close and reopen Claude Desktop
3. **Check Configuration**: Verify the `claude_desktop_config.json` file is correct
4. **API Key**: Ensure your Private Integration API key is still valid

## 📋 Expected Results

When working correctly, you should see:
- ✅ Detailed contact information from your GoHighLevel account
- ✅ Ability to create, update, and search contacts
- ✅ SMS and email sending capabilities
- ✅ Opportunity and pipeline management
- ✅ Calendar and appointment functionality

## 🚀 Next Steps

Once verified, you can:
1. **Automate Lead Management**: Use the MCP to automatically tag and categorize leads
2. **Set Up Workflows**: Create automated follow-up sequences
3. **Integrate with Other Tools**: Combine with other MCP servers for complete automation
4. **Build Custom Workflows**: Create sophisticated real estate wholesaling automation

---

**🎯 Your GoHighLevel MCP Server Configuration:**
- **API Key**: `pit-e2900a68-36d8-4e40-83d2-5eb1c3f23a4b`
- **Location ID**: `dDNFLruYtwNAho9Uay2N`
- **Tools Available**: 253
- **Status**: ✅ Ready for Production
