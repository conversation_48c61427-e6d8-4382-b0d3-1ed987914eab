#!/usr/bin/env node

/**
 * Test script to debug GoHighLevel API authentication
 */

const axios = require('axios');
require('dotenv').config();

const API_KEY = process.env.GHL_API_KEY;
const LOCATION_ID = process.env.GHL_LOCATION_ID;

console.log('🧪 Testing GoHighLevel API Authentication');
console.log('=====================================');
console.log(`API Key: ${API_KEY ? API_KEY.substring(0, 20) + '...' : 'NOT SET'}`);
console.log(`Location ID: ${LOCATION_ID || 'NOT SET'}`);
console.log('');

if (!API_KEY || !LOCATION_ID) {
    console.log('❌ Missing required environment variables');
    console.log('Please set GHL_API_KEY and GHL_LOCATION_ID in your .env file');
    process.exit(1);
}

// First, let's decode the JWT to see what's inside
function decodeJWT(token) {
    try {
        const parts = token.split('.');
        if (parts.length !== 3) {
            return { error: 'Invalid JWT format' };
        }

        const header = JSON.parse(Buffer.from(parts[0], 'base64').toString());
        const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());

        return { header, payload };
    } catch (error) {
        return { error: error.message };
    }
}

console.log('🔍 JWT Token Analysis:');
const jwtInfo = decodeJWT(API_KEY);
if (jwtInfo.error) {
    console.log(`❌ JWT Decode Error: ${jwtInfo.error}`);
} else {
    console.log('📋 JWT Header:', JSON.stringify(jwtInfo.header, null, 2));
    console.log('📋 JWT Payload:', JSON.stringify(jwtInfo.payload, null, 2));

    // Check if token is expired
    if (jwtInfo.payload.exp) {
        const expDate = new Date(jwtInfo.payload.exp * 1000);
        const now = new Date();
        console.log(`⏰ Token expires: ${expDate.toISOString()}`);
        console.log(`⏰ Current time: ${now.toISOString()}`);
        console.log(`⏰ Token expired: ${now > expDate ? '❌ YES' : '✅ NO'}`);
    } else if (jwtInfo.payload.iat) {
        const issuedDate = new Date(jwtInfo.payload.iat);
        console.log(`⏰ Token issued: ${issuedDate.toISOString()}`);
    }
}
console.log('');

// Test configurations to try
const testConfigs = [
    {
        name: 'Standard Location API (services.leadconnectorhq.com)',
        baseURL: 'https://services.leadconnectorhq.com',
        headers: {
            'Authorization': `Bearer ${API_KEY}`,
            'Version': '2021-07-28',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    },
    {
        name: 'Without Bearer prefix',
        baseURL: 'https://services.leadconnectorhq.com',
        headers: {
            'Authorization': API_KEY,
            'Version': '2021-07-28',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    },
    {
        name: 'With X-API-Key header',
        baseURL: 'https://services.leadconnectorhq.com',
        headers: {
            'X-API-Key': API_KEY,
            'Version': '2021-07-28',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    },
    {
        name: 'Legacy API endpoint',
        baseURL: 'https://rest.gohighlevel.com/v1',
        headers: {
            'Authorization': `Bearer ${API_KEY}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    }
];

async function testConfig(config) {
    console.log(`\n🔍 Testing: ${config.name}`);
    console.log(`   Base URL: ${config.baseURL}`);
    console.log(`   Headers: ${JSON.stringify(config.headers, null, 6)}`);
    
    try {
        const response = await axios.get('/contacts/', {
            baseURL: config.baseURL,
            headers: config.headers,
            params: {
                locationId: LOCATION_ID,
                limit: 1
            },
            timeout: 10000
        });
        
        console.log(`   ✅ SUCCESS: Status ${response.status}`);
        console.log(`   📊 Response: ${JSON.stringify(response.data, null, 6)}`);
        return true;
    } catch (error) {
        console.log(`   ❌ FAILED: ${error.response?.status || 'Network Error'}`);
        console.log(`   📝 Error: ${error.response?.data?.message || error.message}`);
        if (error.response?.data) {
            console.log(`   📋 Details: ${JSON.stringify(error.response.data, null, 6)}`);
        }
        return false;
    }
}

async function main() {
    let successCount = 0;
    
    for (const config of testConfigs) {
        const success = await testConfig(config);
        if (success) successCount++;
    }
    
    console.log('\n📊 Summary:');
    console.log('===========');
    console.log(`✅ Successful configs: ${successCount}/${testConfigs.length}`);
    
    if (successCount === 0) {
        console.log('\n🔧 Troubleshooting Steps:');
        console.log('1. Verify your GHL_API_KEY is current and valid');
        console.log('2. Check that your GoHighLevel account is active');
        console.log('3. Ensure you\'re using the Location API Key (not Private Integrations)');
        console.log('4. Try generating a new Location API Key from: Settings → Company → API Key');
        console.log('5. Verify your Location ID is correct');
    }
}

main().catch(console.error);
