#!/usr/bin/env node

/**
 * Test script to verify MCP server is working correctly
 * This simulates how <PERSON> would interact with the MCP server
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🧪 Testing GoHighLevel MCP Server Connection');
console.log('===========================================');

// Start the MCP server
const serverPath = path.join(__dirname, 'dist', 'server.js');
console.log(`📍 Server path: ${serverPath}`);

const server = spawn('node', [serverPath], {
    stdio: ['pipe', 'pipe', 'pipe'],
    env: {
        ...process.env,
        GHL_API_KEY: 'pit-e2900a68-36d8-4e40-83d2-5eb1c3f23a4b',
        GHL_LOCATION_ID: 'dDNFLruYtwNAho9Uay2N'
    }
});

let serverReady = false;
let responseBuffer = '';

// Handle server output
server.stdout.on('data', (data) => {
    const output = data.toString();
    console.log('📤 Server output:', output.trim());
    
    if (output.includes('Ready to handle Claude Desktop requests')) {
        serverReady = true;
        console.log('✅ Server is ready! Testing MCP protocol...');
        testMCPProtocol();
    }
});

server.stderr.on('data', (data) => {
    console.log('❌ Server error:', data.toString().trim());
});

// Test MCP protocol
function testMCPProtocol() {
    console.log('\n🔍 Testing MCP Protocol Communication');
    console.log('=====================================');
    
    // Test 1: Initialize request
    const initRequest = {
        jsonrpc: "2.0",
        id: 1,
        method: "initialize",
        params: {
            protocolVersion: "2024-11-05",
            capabilities: {
                roots: {
                    listChanged: true
                },
                sampling: {}
            },
            clientInfo: {
                name: "test-client",
                version: "1.0.0"
            }
        }
    };
    
    console.log('📨 Sending initialize request...');
    server.stdin.write(JSON.stringify(initRequest) + '\n');
    
    // Wait for response
    setTimeout(() => {
        // Test 2: List tools request
        const toolsRequest = {
            jsonrpc: "2.0",
            id: 2,
            method: "tools/list",
            params: {}
        };
        
        console.log('📨 Sending tools/list request...');
        server.stdin.write(JSON.stringify(toolsRequest) + '\n');
        
        // Wait and then test a simple tool call
        setTimeout(() => {
            // Test 3: Simple tool call
            const toolCallRequest = {
                jsonrpc: "2.0",
                id: 3,
                method: "tools/call",
                params: {
                    name: "search_contacts",
                    arguments: {
                        query: "audrey"
                    }
                }
            };
            
            console.log('📨 Sending search_contacts tool call...');
            server.stdin.write(JSON.stringify(toolCallRequest) + '\n');
            
            // Clean up after tests
            setTimeout(() => {
                console.log('\n✅ MCP Protocol tests completed!');
                console.log('🔄 Terminating test server...');
                server.kill();
                process.exit(0);
            }, 3000);
            
        }, 2000);
    }, 2000);
}

// Handle server responses
server.stdout.on('data', (data) => {
    responseBuffer += data.toString();
    
    // Try to parse JSON responses
    const lines = responseBuffer.split('\n');
    responseBuffer = lines.pop() || ''; // Keep incomplete line
    
    lines.forEach(line => {
        if (line.trim()) {
            try {
                const response = JSON.parse(line);
                console.log('📥 MCP Response:', JSON.stringify(response, null, 2));
            } catch (e) {
                // Not JSON, probably server log
                if (!line.includes('[GHL MCP]') && !line.includes('🚀')) {
                    console.log('📄 Server log:', line.trim());
                }
            }
        }
    });
});

// Handle process exit
server.on('close', (code) => {
    console.log(`\n🏁 Server process exited with code ${code}`);
});

// Timeout safety
setTimeout(() => {
    console.log('\n⏰ Test timeout reached, terminating...');
    server.kill();
    process.exit(1);
}, 15000);
