#!/usr/bin/env python3
"""
Test Automation System - Verify the Automated Deal Closing Machine
Tests all components and workflows to ensure proper operation
"""

import asyncio
import logging
import json
import os
import sys
import requests
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class AutomationSystemTester:
    """
    Comprehensive tester for the automated deal closing machine
    """
    
    def __init__(self):
        self.base_url = "http://localhost:5000"
        self.test_results = []
        
    def run_test(self, test_name: str, test_func):
        """Run a single test and record results"""
        logger.info(f"🧪 Running test: {test_name}")
        
        try:
            result = test_func()
            if result:
                logger.info(f"✅ {test_name} - PASSED")
                self.test_results.append({"test": test_name, "status": "PASSED", "error": None})
                return True
            else:
                logger.error(f"❌ {test_name} - FAILED")
                self.test_results.append({"test": test_name, "status": "FAILED", "error": "Test returned False"})
                return False
        except Exception as e:
            logger.error(f"❌ {test_name} - ERROR: {e}")
            self.test_results.append({"test": test_name, "status": "ERROR", "error": str(e)})
            return False
    
    async def run_async_test(self, test_name: str, test_func):
        """Run an async test and record results"""
        logger.info(f"🧪 Running async test: {test_name}")
        
        try:
            result = await test_func()
            if result:
                logger.info(f"✅ {test_name} - PASSED")
                self.test_results.append({"test": test_name, "status": "PASSED", "error": None})
                return True
            else:
                logger.error(f"❌ {test_name} - FAILED")
                self.test_results.append({"test": test_name, "status": "FAILED", "error": "Test returned False"})
                return False
        except Exception as e:
            logger.error(f"❌ {test_name} - ERROR: {e}")
            self.test_results.append({"test": test_name, "status": "ERROR", "error": str(e)})
            return False
    
    def test_api_health(self):
        """Test API server health"""
        try:
            response = requests.get(f"{self.base_url}/api/v1/workflows/health", timeout=10)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"API health check failed: {e}")
            return False
    
    def test_ghl_mcp_connection(self):
        """Test GoHighLevel MCP connection"""
        try:
            from agents.ghl_mcp_client import GHLMCPClient
            client = GHLMCPClient()
            
            # Try to search for contacts (this will test the connection)
            result = client.search_contacts(query="test", limit=1)
            return result is not None
        except Exception as e:
            logger.error(f"GHL MCP connection test failed: {e}")
            return False
    
    def test_workflow_configuration(self):
        """Test workflow configuration loading"""
        try:
            config_path = "config/workflows.json"
            if not os.path.exists(config_path):
                return False
            
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            # Check for required configuration sections
            required_sections = [
                "automated_deal_pipeline",
                "agent_communication",
                "comping"
            ]
            
            for section in required_sections:
                if section not in config:
                    logger.error(f"Missing configuration section: {section}")
                    return False
            
            return True
        except Exception as e:
            logger.error(f"Workflow configuration test failed: {e}")
            return False
    
    async def test_master_orchestrator(self):
        """Test master deal orchestrator"""
        try:
            from agents.workflows.master_deal_orchestrator import MasterDealOrchestrator
            
            # Create test lead data
            test_lead_data = {
                "id": "test_lead_123",
                "first_name": "John",
                "last_name": "Doe",
                "email": "<EMAIL>",
                "phone": "+1234567890",
                "address": "123 Test St, Test City, TX 12345",
                "tier": 1,
                "tags": "tier 1, motivated seller"
            }
            
            orchestrator = MasterDealOrchestrator("test_lead_123", test_lead_data)
            
            # Test initialization
            return orchestrator is not None
        except Exception as e:
            logger.error(f"Master orchestrator test failed: {e}")
            return False
    
    async def test_ghl_integration(self):
        """Test GoHighLevel integration"""
        try:
            from agents.workflows.ghl_mcp_integration import GHLMCPIntegration
            
            integration = GHLMCPIntegration()
            
            # Test initialization
            return integration is not None
        except Exception as e:
            logger.error(f"GHL integration test failed: {e}")
            return False
    
    def test_agent_imports(self):
        """Test that all required agents can be imported"""
        try:
            # Test core agent imports
            from agents.lead_scoring_bot import LeadScoringBot
            from agents.mao_calculator import MAOCalculator
            from agents.offer_generator import generate_property_offer
            from agents.followup_bot import FollowUpBot
            from agents.workflows.tier_classifier import classify_lead_tier
            from agents.workflows.agent_communication_workflow import execute_agent_communication
            
            logger.info("All agent imports successful")
            return True
        except Exception as e:
            logger.error(f"Agent import test failed: {e}")
            return False
    
    def test_data_sources(self):
        """Test data source integrations"""
        try:
            # Test RentCast
            from agents.rentcast_agent import get_rentcast_comps
            
            # Test Realie.ai
            from agents.realie_agent import get_realie_comparables
            
            # Test Melissa
            from agents.melissa_agent import get_melissa_property_data
            
            logger.info("All data source imports successful")
            return True
        except Exception as e:
            logger.error(f"Data source test failed: {e}")
            return False
    
    def test_environment_variables(self):
        """Test required environment variables"""
        try:
            required_vars = [
                "SUPABASE_URL",
                "SUPABASE_ANON_KEY",
                "OPENAI_API_KEY"
            ]
            
            missing_vars = []
            for var in required_vars:
                if not os.getenv(var):
                    missing_vars.append(var)
            
            if missing_vars:
                logger.error(f"Missing environment variables: {missing_vars}")
                return False
            
            return True
        except Exception as e:
            logger.error(f"Environment variables test failed: {e}")
            return False
    
    def test_log_directories(self):
        """Test that log directories exist"""
        try:
            required_dirs = ["logs", "memory_store", "data"]
            
            for dir_name in required_dirs:
                if not os.path.exists(dir_name):
                    logger.error(f"Missing directory: {dir_name}")
                    return False
            
            return True
        except Exception as e:
            logger.error(f"Log directories test failed: {e}")
            return False
    
    async def run_all_tests(self):
        """Run all tests"""
        logger.info("🚀 Starting Automated Deal Closing Machine Tests")
        logger.info("=" * 60)
        
        # Synchronous tests
        sync_tests = [
            ("Environment Variables", self.test_environment_variables),
            ("Log Directories", self.test_log_directories),
            ("Workflow Configuration", self.test_workflow_configuration),
            ("Agent Imports", self.test_agent_imports),
            ("Data Sources", self.test_data_sources),
            ("API Health", self.test_api_health),
            ("GHL MCP Connection", self.test_ghl_mcp_connection),
        ]
        
        for test_name, test_func in sync_tests:
            self.run_test(test_name, test_func)
        
        # Asynchronous tests
        async_tests = [
            ("Master Orchestrator", self.test_master_orchestrator),
            ("GHL Integration", self.test_ghl_integration),
        ]
        
        for test_name, test_func in async_tests:
            await self.run_async_test(test_name, test_func)
        
        # Generate test report
        self.generate_test_report()
    
    def generate_test_report(self):
        """Generate and display test report"""
        logger.info("=" * 60)
        logger.info("📊 TEST RESULTS SUMMARY")
        logger.info("=" * 60)
        
        passed = len([r for r in self.test_results if r["status"] == "PASSED"])
        failed = len([r for r in self.test_results if r["status"] == "FAILED"])
        errors = len([r for r in self.test_results if r["status"] == "ERROR"])
        total = len(self.test_results)
        
        logger.info(f"Total Tests: {total}")
        logger.info(f"✅ Passed: {passed}")
        logger.info(f"❌ Failed: {failed}")
        logger.info(f"🚨 Errors: {errors}")
        logger.info(f"Success Rate: {(passed/total)*100:.1f}%")
        
        # Show failed tests
        failed_tests = [r for r in self.test_results if r["status"] != "PASSED"]
        if failed_tests:
            logger.info("\n❌ FAILED TESTS:")
            for test in failed_tests:
                logger.info(f"  - {test['test']}: {test['error']}")
        
        # Save detailed report
        report_file = f"logs/test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "summary": {
                    "total": total,
                    "passed": passed,
                    "failed": failed,
                    "errors": errors,
                    "success_rate": (passed/total)*100
                },
                "results": self.test_results
            }, f, indent=2)
        
        logger.info(f"\n📄 Detailed report saved to: {report_file}")
        
        if passed == total:
            logger.info("\n🎉 ALL TESTS PASSED! Your automated deal closing machine is ready!")
        else:
            logger.info(f"\n⚠️  {failed + errors} tests failed. Please fix the issues before deployment.")

async def main():
    """Main test function"""
    tester = AutomationSystemTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
