"""
Error Recovery System for AI-OS
Handles failures gracefully and implements automatic recovery
"""

import logging
import time
import json
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import asyncio
from functools import wraps

logger = logging.getLogger(__name__)

class ErrorSeverity(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class RecoveryAction(Enum):
    RETRY = "retry"
    FALLBACK = "fallback"
    SKIP = "skip"
    ESCALATE = "escalate"
    RESTART_SERVICE = "restart_service"

@dataclass
class ErrorContext:
    """Context information for an error"""
    error_type: str
    error_message: str
    service: str
    operation: str
    lead_id: Optional[str] = None
    timestamp: datetime = None
    stack_trace: Optional[str] = None
    input_data: Optional[Dict] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class RecoveryStrategy:
    """Recovery strategy configuration"""
    error_pattern: str
    severity: ErrorSeverity
    action: RecoveryAction
    max_retries: int = 3
    retry_delay: float = 1.0
    fallback_function: Optional[Callable] = None
    escalation_threshold: int = 5

class ErrorRecoverySystem:
    """Main error recovery system"""
    
    def __init__(self):
        self.strategies = self._load_recovery_strategies()
        self.error_counts = {}
        self.circuit_breakers = {}
        self.recovery_history = []
        
    def _load_recovery_strategies(self) -> List[RecoveryStrategy]:
        """Load recovery strategies for different error types"""
        return [
            # API Connection Errors
            RecoveryStrategy(
                error_pattern="connection.*timeout|connection.*refused",
                severity=ErrorSeverity.HIGH,
                action=RecoveryAction.RETRY,
                max_retries=3,
                retry_delay=2.0
            ),
            
            # Rate Limiting Errors
            RecoveryStrategy(
                error_pattern="rate.*limit|429|too.*many.*requests",
                severity=ErrorSeverity.MEDIUM,
                action=RecoveryAction.RETRY,
                max_retries=5,
                retry_delay=5.0
            ),
            
            # Authentication Errors
            RecoveryStrategy(
                error_pattern="unauthorized|401|invalid.*token",
                severity=ErrorSeverity.HIGH,
                action=RecoveryAction.ESCALATE,
                max_retries=1
            ),
            
            # Database Connection Errors
            RecoveryStrategy(
                error_pattern="database.*connection|postgres.*error",
                severity=ErrorSeverity.CRITICAL,
                action=RecoveryAction.RETRY,
                max_retries=3,
                retry_delay=3.0
            ),
            
            # OpenAI API Errors
            RecoveryStrategy(
                error_pattern="openai.*error|embedding.*failed",
                severity=ErrorSeverity.MEDIUM,
                action=RecoveryAction.FALLBACK,
                max_retries=2,
                fallback_function=self._openai_fallback
            ),
            
            # GoHighLevel API Errors
            RecoveryStrategy(
                error_pattern="ghl.*error|gohighlevel.*failed",
                severity=ErrorSeverity.HIGH,
                action=RecoveryAction.RETRY,
                max_retries=3,
                retry_delay=2.0
            ),
            
            # Memory/Resource Errors
            RecoveryStrategy(
                error_pattern="memory.*error|out.*of.*memory",
                severity=ErrorSeverity.CRITICAL,
                action=RecoveryAction.RESTART_SERVICE,
                max_retries=1
            ),
            
            # General Network Errors
            RecoveryStrategy(
                error_pattern="network.*error|dns.*resolution",
                severity=ErrorSeverity.MEDIUM,
                action=RecoveryAction.RETRY,
                max_retries=3,
                retry_delay=1.0
            )
        ]
    
    def handle_error(self, error: Exception, context: ErrorContext) -> bool:
        """
        Handle an error and attempt recovery
        
        Returns:
            bool: True if recovery was successful, False otherwise
        """
        logger.error(f"Handling error in {context.service}.{context.operation}: {error}")
        
        # Find matching recovery strategy
        strategy = self._find_recovery_strategy(str(error))
        
        if not strategy:
            logger.warning(f"No recovery strategy found for error: {error}")
            return False
        
        # Check circuit breaker
        if self._is_circuit_open(context.service, context.operation):
            logger.warning(f"Circuit breaker open for {context.service}.{context.operation}")
            return False
        
        # Execute recovery action
        success = self._execute_recovery(error, context, strategy)
        
        # Update circuit breaker state
        self._update_circuit_breaker(context.service, context.operation, success)
        
        # Log recovery attempt
        self._log_recovery_attempt(context, strategy, success)
        
        return success
    
    def _find_recovery_strategy(self, error_message: str) -> Optional[RecoveryStrategy]:
        """Find the best recovery strategy for an error"""
        import re
        
        for strategy in self.strategies:
            if re.search(strategy.error_pattern, error_message.lower()):
                return strategy
        
        return None
    
    def _execute_recovery(self, error: Exception, context: ErrorContext, strategy: RecoveryStrategy) -> bool:
        """Execute the recovery action"""
        if strategy.action == RecoveryAction.RETRY:
            return self._retry_operation(error, context, strategy)
        elif strategy.action == RecoveryAction.FALLBACK:
            return self._execute_fallback(error, context, strategy)
        elif strategy.action == RecoveryAction.SKIP:
            return self._skip_operation(error, context, strategy)
        elif strategy.action == RecoveryAction.ESCALATE:
            return self._escalate_error(error, context, strategy)
        elif strategy.action == RecoveryAction.RESTART_SERVICE:
            return self._restart_service(error, context, strategy)
        
        return False
    
    def _retry_operation(self, error: Exception, context: ErrorContext, strategy: RecoveryStrategy) -> bool:
        """Retry the failed operation"""
        error_key = f"{context.service}.{context.operation}"
        
        if error_key not in self.error_counts:
            self.error_counts[error_key] = 0
        
        self.error_counts[error_key] += 1
        
        if self.error_counts[error_key] > strategy.max_retries:
            logger.error(f"Max retries exceeded for {error_key}")
            return False
        
        logger.info(f"Retrying {error_key} (attempt {self.error_counts[error_key]}/{strategy.max_retries})")
        
        # Wait before retry
        time.sleep(strategy.retry_delay)
        
        # Reset error count on successful retry (this would be handled by the calling code)
        return True  # Indicates retry should be attempted
    
    def _execute_fallback(self, error: Exception, context: ErrorContext, strategy: RecoveryStrategy) -> bool:
        """Execute fallback function"""
        if not strategy.fallback_function:
            logger.warning(f"No fallback function defined for {context.service}.{context.operation}")
            return False
        
        try:
            logger.info(f"Executing fallback for {context.service}.{context.operation}")
            result = strategy.fallback_function(context)
            return result is not None
        except Exception as fallback_error:
            logger.error(f"Fallback function failed: {fallback_error}")
            return False
    
    def _skip_operation(self, error: Exception, context: ErrorContext, strategy: RecoveryStrategy) -> bool:
        """Skip the failed operation"""
        logger.info(f"Skipping operation {context.service}.{context.operation} due to error")
        return True  # Consider skipping as successful recovery
    
    def _escalate_error(self, error: Exception, context: ErrorContext, strategy: RecoveryStrategy) -> bool:
        """Escalate error to human intervention"""
        logger.critical(f"Escalating error in {context.service}.{context.operation}: {error}")
        
        # Send alert to monitoring system
        try:
            from monitoring.production_monitoring import monitor
            alert_check = type('HealthCheck', (), {
                'service': context.service,
                'status': 'critical',
                'response_time': 0,
                'message': f"Escalated error: {error}",
                'timestamp': datetime.now()
            })()
            
            alert_config = type('Alert', (), {
                'name': 'escalated_error',
                'severity': 'critical'
            })()
            
            monitor.send_alert(alert_check, alert_config)
        except Exception as alert_error:
            logger.error(f"Failed to send escalation alert: {alert_error}")
        
        return False  # Escalation means we couldn't recover
    
    def _restart_service(self, error: Exception, context: ErrorContext, strategy: RecoveryStrategy) -> bool:
        """Restart the affected service"""
        logger.critical(f"Attempting service restart for {context.service}")
        
        # This would typically trigger a service restart
        # Implementation depends on deployment method (Docker, systemd, etc.)
        try:
            import subprocess
            result = subprocess.run(
                ["docker-compose", "restart", context.service],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                logger.info(f"Successfully restarted {context.service}")
                return True
            else:
                logger.error(f"Failed to restart {context.service}: {result.stderr}")
                return False
                
        except Exception as restart_error:
            logger.error(f"Error during service restart: {restart_error}")
            return False
    
    def _is_circuit_open(self, service: str, operation: str) -> bool:
        """Check if circuit breaker is open"""
        circuit_key = f"{service}.{operation}"
        
        if circuit_key not in self.circuit_breakers:
            return False
        
        circuit = self.circuit_breakers[circuit_key]
        
        # Check if circuit should be reset
        if circuit['state'] == 'open':
            time_since_open = time.time() - circuit['opened_at']
            if time_since_open > circuit['timeout']:
                circuit['state'] = 'half_open'
                logger.info(f"Circuit breaker for {circuit_key} moved to half-open")
        
        return circuit['state'] == 'open'
    
    def _update_circuit_breaker(self, service: str, operation: str, success: bool):
        """Update circuit breaker state"""
        circuit_key = f"{service}.{operation}"
        
        if circuit_key not in self.circuit_breakers:
            self.circuit_breakers[circuit_key] = {
                'state': 'closed',
                'failure_count': 0,
                'opened_at': 0,
                'timeout': 60  # 1 minute timeout
            }
        
        circuit = self.circuit_breakers[circuit_key]
        
        if success:
            circuit['failure_count'] = 0
            if circuit['state'] == 'half_open':
                circuit['state'] = 'closed'
                logger.info(f"Circuit breaker for {circuit_key} closed")
        else:
            circuit['failure_count'] += 1
            
            if circuit['failure_count'] >= 5 and circuit['state'] == 'closed':
                circuit['state'] = 'open'
                circuit['opened_at'] = time.time()
                logger.warning(f"Circuit breaker for {circuit_key} opened")
    
    def _log_recovery_attempt(self, context: ErrorContext, strategy: RecoveryStrategy, success: bool):
        """Log recovery attempt for analysis"""
        recovery_record = {
            'timestamp': datetime.now().isoformat(),
            'service': context.service,
            'operation': context.operation,
            'error_type': context.error_type,
            'error_message': context.error_message,
            'strategy_action': strategy.action.value,
            'strategy_severity': strategy.severity.value,
            'success': success,
            'lead_id': context.lead_id
        }
        
        self.recovery_history.append(recovery_record)
        
        # Keep only last 1000 records
        if len(self.recovery_history) > 1000:
            self.recovery_history = self.recovery_history[-1000:]
    
    def _openai_fallback(self, context: ErrorContext) -> Optional[str]:
        """Fallback for OpenAI API failures"""
        logger.info("Using OpenAI fallback - returning cached or simplified response")
        
        # Return a simple fallback response
        if "embedding" in context.operation.lower():
            # Return zero vector for embedding failures
            return [0.0] * 1536  # Standard embedding dimension
        elif "summary" in context.operation.lower():
            # Return simple summary
            return "Summary unavailable due to API error"
        else:
            # Return generic fallback
            return "AI processing temporarily unavailable"
    
    def get_recovery_stats(self) -> Dict[str, Any]:
        """Get recovery system statistics"""
        total_attempts = len(self.recovery_history)
        successful_recoveries = sum(1 for r in self.recovery_history if r['success'])
        
        # Group by service
        service_stats = {}
        for record in self.recovery_history:
            service = record['service']
            if service not in service_stats:
                service_stats[service] = {'total': 0, 'successful': 0}
            service_stats[service]['total'] += 1
            if record['success']:
                service_stats[service]['successful'] += 1
        
        return {
            'total_recovery_attempts': total_attempts,
            'successful_recoveries': successful_recoveries,
            'success_rate': successful_recoveries / total_attempts if total_attempts > 0 else 0,
            'service_stats': service_stats,
            'active_circuit_breakers': {
                k: v for k, v in self.circuit_breakers.items() 
                if v['state'] != 'closed'
            }
        }

# Global recovery system instance
recovery_system = ErrorRecoverySystem()

def with_error_recovery(service: str, operation: str):
    """Decorator for automatic error recovery"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                result = func(*args, **kwargs)
                # Reset error count on success
                error_key = f"{service}.{operation}"
                if error_key in recovery_system.error_counts:
                    recovery_system.error_counts[error_key] = 0
                return result
            except Exception as e:
                context = ErrorContext(
                    error_type=type(e).__name__,
                    error_message=str(e),
                    service=service,
                    operation=operation,
                    input_data={'args': args, 'kwargs': kwargs}
                )
                
                # Attempt recovery
                if recovery_system.handle_error(e, context):
                    # Retry the operation
                    return func(*args, **kwargs)
                else:
                    # Recovery failed, re-raise the error
                    raise
        return wrapper
    return decorator
